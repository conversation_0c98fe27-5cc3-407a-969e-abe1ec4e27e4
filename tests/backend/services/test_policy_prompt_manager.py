"""
Unit tests for the PolicyPromptManager class.
"""
import pytest
from backend.services.enhanced_policy_generator import PolicyPromptManager, PolicyRequest, StageOutput # Added StageOutput


class TestPolicyPromptManager:
    """Test suite for PolicyPromptManager."""

    @pytest.fixture
    def prompt_manager(self):
        """Fixture to provide a PolicyPromptManager instance."""
        return PolicyPromptManager()

    @pytest.fixture
    def mock_policy_request(self):
        """Fixture for a sample PolicyRequest."""
        return PolicyRequest(
            framework="ISO 27001",
            company_name="TestComp",
            company_type="Tech",
            industry="SaaS",
            employee_count=100,
            data_types=["PII", "Financial"],
            existing_policies=["Basic Privacy Policy"],
            complexity_level="medium",
            user_id="test_user_prompt_mgr",
            jurisdiction="EU",
            risk_profile="high"
        )

    @pytest.fixture
    def mock_stage_output(self):
        """Fixture for a sample StageOutput."""
        return StageOutput(
            content="This is mock stage content.",
            word_count=5,
            completeness_score=0.9,
            generation_time=1.0,
            metadata={}
        )

    def test_initialization(self, prompt_manager):
        """Test that PolicyPromptManager initializes correctly."""
        assert prompt_manager is not None
        assert hasattr(prompt_manager, 'FRAMEWORK_PROMPT')
        assert hasattr(prompt_manager, 'PROCEDURES_PROMPT')
        assert hasattr(prompt_manager, 'TOOLS_PROMPT')

    def test_get_framework_prompt(self, prompt_manager, mock_policy_request):
        """Test get_framework_prompt correctly formats the framework prompt."""
        prompt = prompt_manager.get_framework_prompt(mock_policy_request)

        assert isinstance(prompt, str)
        assert len(prompt) > 0
        # Check if placeholders from PolicyRequest are filled
        assert mock_policy_request.company_name in prompt
        assert mock_policy_request.industry in prompt
        assert str(mock_policy_request.employee_count) in prompt
        assert mock_policy_request.jurisdiction in prompt
        assert mock_policy_request.risk_profile in prompt
        assert ", ".join(mock_policy_request.data_types) in prompt

        # Check that original template placeholders are gone
        assert "{company_name}" not in prompt
        assert "{industry}" not in prompt

    def test_get_procedures_prompt(self, prompt_manager, mock_policy_request, mock_stage_output):
        """Test get_procedures_prompt correctly formats the procedures prompt."""
        mock_framework_stage_output = mock_stage_output # Use the fixture
        prompt = prompt_manager.get_procedures_prompt(mock_framework_stage_output, mock_policy_request)

        assert isinstance(prompt, str)
        assert len(prompt) > 0
        assert mock_policy_request.company_name in prompt
        # Check that individual components are in the prompt (based on actual template structure)
        assert mock_policy_request.company_type in prompt
        assert str(mock_policy_request.employee_count) in prompt
        assert mock_framework_stage_output.content in prompt # Check framework content is included
        assert "{company_name}" not in prompt
        assert "{framework_content}" not in prompt # Placeholder name in prompt template

    def test_get_tools_prompt(self, prompt_manager, mock_policy_request, mock_stage_output):
        """Test get_tools_prompt correctly formats the tools prompt."""
        # Create framework output (required parameter)
        mock_framework_so = StageOutput(
            content="Mock framework content for tools prompt.",
            word_count=5, completeness_score=0.9, generation_time=1.0, metadata={}
        )
        # Create a distinct StageOutput for procedures if its content needs to be different
        mock_procedures_so = StageOutput(
            content="Mock procedures content for tools prompt.",
            word_count=6, completeness_score=0.8, generation_time=1.1, metadata={}
        )
        # Call with the correct signature (framework_output, procedures_output, request)
        prompt = prompt_manager.get_tools_prompt(mock_framework_so, mock_procedures_so, mock_policy_request)

        assert isinstance(prompt, str)
        assert len(prompt) > 0
        assert mock_policy_request.company_name in prompt
        # Check that individual components are in the prompt (based on actual template structure)
        assert mock_policy_request.company_type in prompt
        assert mock_policy_request.industry in prompt
        assert str(mock_policy_request.employee_count) in prompt
        assert mock_framework_so.content in prompt # Check framework content is included
        assert mock_procedures_so.content in prompt # Check procedures content is included

        assert "{company_name}" not in prompt
        assert "{procedures_content}" not in prompt
        assert "{company_specifics}" not in prompt
        assert "{employee_count}" not in prompt
