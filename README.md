# ComplianceGPT
## AI-Powered Compliance Automation for UK SMBs

**Transform compliance from months to days with AI-powered automation.**

ComplianceGPT democratizes enterprise-grade compliance for UK SMBs, enabling rapid achievement of GDPR, SOC 2, and ISO 27001 compliance at a fraction of traditional consultant costs.

---

## 🎯 **Value Proposition**

- **⚡ 95% Time Reduction**: From 200+ hours to under 20 hours annually
- **💰 80% Cost Savings**: £50K consultant fees to affordable SaaS pricing  
- **🚀 Days Not Months**: Achieve compliance in days, not months
- **🎨 AI-Generated Policies**: Professional, audit-ready policies using Gemini 2.5 Flash
- **🇬🇧 UK-Focused**: Built specifically for UK SMB compliance requirements

---

## 🏗️ **Architecture**

### **Frontend** 
- **Framework**: React 19 with Tailwind CSS
- **Features**: Modern, responsive UI optimized for compliance workflows
- **Key Components**: AI Policy Generator, Compliance Dashboard, Project Management

### **Backend**
- **Framework**: FastAPI with async MongoDB
- **AI Integration**: Google Gemini 2.5 Flash for policy generation
- **Features**: RESTful API, real-time compliance tracking, audit preparation

### **AI Engine**
- **Model**: Gemini 2.5 Flash (Basic + Thinking modes)
- **Capabilities**: 
  - Professional policy generation
  - Compliance framework analysis
  - Smart mode switching for complex scenarios
  - Cost-effective at $0.15/$0.60 per M tokens

---

## 🚀 **Getting Started**

### **Prerequisites**
- Docker & Docker Compose
- Google AI API key (Gemini 2.5 Flash)
- Node.js 18+ and Python 3.11+

### **Quick Setup**
```bash
# Clone repository
git clone <repository-url>
cd compliancegpt

# Set environment variables
echo "GEMINI_API_KEY=your_api_key_here" > backend/.env
echo "MONGO_URL=mongodb://localhost:27017/" >> backend/.env

# Start services
docker-compose up -d

# Install dependencies
cd frontend && yarn install
cd ../backend && pip install -r requirements.txt

# Launch application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8001
```

---

## 🎯 **Core Features**

### **1. AI Policy Generator**
Generate comprehensive compliance policies using Gemini 2.5 Flash:
- **GDPR**: Data protection policies, privacy notices, DPIA templates
- **SOC 2**: Security policies, access controls, incident response
- **ISO 27001**: ISMS policies, risk management, security procedures

### **2. Compliance Dashboard** 
Real-time visibility into compliance status:
- Progress tracking across frameworks
- Evidence collection status
- Upcoming audit deadlines
- Task assignment and notifications

### **3. Project Management**
Structured approach to compliance implementation:
- Framework-specific project templates
- Progress milestones and deadlines
- Team collaboration and task assignments
- Audit preparation workflows

### **4. Evidence Collection**
Automated evidence gathering (roadmap):
- Integration with business tools (Google Workspace, Microsoft 365, Slack)
- Automated screenshot capture
- Evidence freshness tracking
- Gap identification and alerts

---

## 🎯 **Supported Frameworks**

| Framework | Coverage | Key Features |
|-----------|----------|--------------|
| **GDPR** | Complete | Data protection policies, subject rights procedures, breach notifications |
| **SOC 2 Type II** | Complete | Security controls, access management, monitoring procedures |
| **ISO 27001:2022** | Complete | ISMS policies, risk management, security controls |

---

## 📊 **API Endpoints**

### **Core APIs**
```
GET    /api/health              # Health check
GET    /api/frameworks          # Available frameworks
GET    /api/dashboard           # Dashboard data

POST   /api/policies/generate   # Generate AI policy  
GET    /api/policies            # List policies
GET    /api/policies/{id}       # Get specific policy

POST   /api/projects            # Create project
GET    /api/projects            # List projects
GET    /api/projects/{id}       # Get specific project
```

### **Example: Generate GDPR Policy**
```bash
curl -X POST http://localhost:8001/api/policies/generate \
  -H "Content-Type: application/json" \
  -d '{
    "framework": "gdpr",
    "company_name": "TechCorp Ltd", 
    "company_type": "SaaS",
    "employee_count": 25,
    "industry": "Software Development",
    "data_types": ["Personal Data", "Financial Data"],
    "complexity_level": "standard"
  }'
```

---

## 🧪 **Testing**

### **Backend Testing**
```bash
cd backend
python -m pytest tests/ -v
```

### **Frontend Testing**
```bash
cd frontend  
yarn test
```

### **Integration Testing**
```bash
# Run the provided test suite
python backend_test.py
```

---

## 🗂️ **Project Structure**

```
compliancegpt/
├── backend/                 # FastAPI backend
│   ├── __init__.py         # Package initialization
│   ├── server.py           # Main FastAPI application  
│   ├── models.py           # Pydantic data models
│   ├── config.py           # Configuration management
│   └── requirements.txt    # Python dependencies
│
├── frontend/               # React frontend
│   ├── src/
│   │   ├── App.js         # Main React component
│   │   ├── App.css        # Component styles
│   │   └── index.js       # Entry point
│   ├── public/
│   │   └── index.html     # HTML template
│   └── package.json       # Node.js dependencies
│
├── tests/                  # Test suites
├── scripts/               # Utility scripts
└── README.md              # This file
```

---

## 🔐 **Security**

ComplianceGPT implements enterprise-grade security:
- **Encryption**: AES-256 at rest, TLS 1.3 in transit
- **Authentication**: Multi-factor authentication support
- **Access Control**: Role-based permissions
- **Audit Logs**: Complete audit trail for compliance
- **Data Residency**: UK/EU data residency compliance

---

## 🚀 **Roadmap**

### **Phase 1 (Current)**
- ✅ AI Policy Generation (Gemini 2.5 Flash)
- ✅ Core compliance frameworks (GDPR, SOC2, ISO27001)
- ✅ Project and policy management
- ✅ Modern responsive UI

### **Phase 2 (Q2 2025)**
- 🔄 Evidence collection automation
- 🔄 Integration with business tools
- 🔄 Advanced audit preparation
- 🔄 Custom compliance frameworks

### **Phase 3 (Q3 2025)**
- 📋 White-label solution
- 📋 Advanced AI analytics
- 📋 Compliance marketplace
- 📋 Enterprise features

---

## 📈 **Business Impact**

**For UK SMBs (10-200 employees):**
- **Cost Savings**: 99% reduction vs traditional consultants
- **Time Savings**: 95% reduction in compliance overhead
- **Risk Reduction**: Professional, audit-ready policies
- **Growth Enablement**: Fast enterprise client onboarding

**Target ROI**: 10x cost savings in first year

---

## 🤝 **Contributing**

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### **Development Setup**
1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Make changes and test thoroughly
4. Submit pull request with detailed description

---

## 📄 **License**

This project is licensed under the MIT License - see [LICENSE](LICENSE) file for details.

---

## 🆘 **Support**

- **Documentation**: [docs.compliancegpt.com](https://docs.compliancegpt.com)
- **Email**: <EMAIL>
- **Issues**: GitHub Issues tab

---

**Built with ❤️ for UK SMB compliance automation**

*ComplianceGPT - Democratizing enterprise-grade compliance for UK startups*