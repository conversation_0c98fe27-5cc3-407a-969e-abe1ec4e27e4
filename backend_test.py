
import requests
import sys
import json
from datetime import datetime, timedelta

class ComplianceGPTTester:
    def __init__(self, base_url="https://5fb85ea3-d551-4232-b367-a6d8d4edbcbe.preview.emergentagent.com"):
        self.base_url = base_url
        self.tests_run = 0
        self.tests_passed = 0
        self.auth_token = None
        self.user_data = None
        self.policy_id = None

    def run_test(self, name, method, endpoint, expected_status, data=None):
        """Run a single API test"""
        url = f"{self.base_url}/api/{endpoint}"
        headers = {'Content-Type': 'application/json'}
        
        # Add auth token if available
        if self.auth_token:
            headers['Authorization'] = f'Bearer {self.auth_token}'
        
        self.tests_run += 1
        print(f"\n🔍 Testing {name}...")
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=headers)
            
            success = response.status_code == expected_status
            if success:
                self.tests_passed += 1
                print(f"✅ Passed - Status: {response.status_code}")
                return success, response.json() if response.text else {}
            else:
                print(f"❌ Failed - Expected {expected_status}, got {response.status_code}")
                print(f"Response: {response.text}")
                return False, {}

        except Exception as e:
            print(f"❌ Failed - Error: {str(e)}")
            return False, {}

    def test_health_check(self):
        """Test the health check endpoint"""
        return self.run_test(
            "Health Check",
            "GET",
            "health",
            200
        )

    def test_get_frameworks(self):
        """Test getting all frameworks"""
        return self.run_test(
            "Get Frameworks",
            "GET",
            "frameworks",
            200
        )

    def test_get_specific_framework(self):
        """Test getting a specific framework"""
        return self.run_test(
            "Get Specific Framework",
            "GET",
            "frameworks/gdpr",
            200
        )

    def test_get_dashboard(self):
        """Test getting dashboard data with authentication"""
        success, data = self.run_test(
            "Get Dashboard with Authentication",
            "GET",
            "dashboard",
            200
        )
        
        if success:
            # Verify dashboard data structure
            print("Checking dashboard data structure...")
            if 'user' in data:
                print(f"✅ User data present: {data['user']['fullName']} from {data['user']['companyName']}")
                # Verify user data matches authenticated user
                if self.user_data and data['user']['email'] == self.user_data['email']:
                    print("✅ Dashboard shows correct authenticated user data")
                else:
                    print("❌ Dashboard user data doesn't match authenticated user")
                    success = False
            else:
                print("❌ User data missing")
                success = False
                
            if 'stats' in data:
                print(f"✅ Dashboard stats present: {data['stats']}")
            else:
                print("❌ Dashboard stats missing")
                success = False
                
            if 'frameworks' in data:
                print(f"✅ Frameworks data present: {len(data['frameworks'])} frameworks")
            else:
                print("❌ Frameworks data missing")
                success = False
                
            if 'recent_projects' in data:
                print(f"✅ Recent projects data present: {len(data['recent_projects'])} projects")
            else:
                print("❌ Recent projects data missing")
                success = False
                
            if 'recent_policies' in data:
                print(f"✅ Recent policies data present: {len(data['recent_policies'])} policies")
            else:
                print("❌ Recent policies data missing")
                success = False
        
        return success, data

    def test_get_stripe_config(self):
        """Test getting Stripe configuration"""
        return self.run_test(
            "Get Stripe Config",
            "GET",
            "payments/config",
            200
        )
        
    def test_get_policies(self):
        """Test getting all policies for authenticated user"""
        success, data = self.run_test(
            "Get Policies for Authenticated User",
            "GET",
            "policies",
            200
        )
        
        if success:
            print(f"Found {len(data.get('policies', []))} policies for authenticated user")
        
        return success, data
        
    def test_get_projects(self):
        """Test getting all projects"""
        return self.run_test(
            "Get Projects",
            "GET",
            "projects",
            200
        )
        
    def test_generate_policy(self, company_name):
        """Test policy generation with authenticated user"""
        policy_data = {
            "framework": "GDPR",
            "company_name": company_name,
            "company_type": "Technology Company",
            "employee_count": 85,
            "industry": "Technology",
            "data_types": ["Personal Data", "Email Addresses", "Financial Information", "Health Data", "Location Data"],
            "existing_policies": [],
            "complexity_level": "standard"
        }
        
        success, response = self.run_test(
            "Generate Policy with Authentication",
            "POST",
            "policies/generate",
            200,
            data=policy_data
        )
        
        if success:
            if 'policy_id' in response:
                self.policy_id = response['policy_id']
                print(f"✅ Policy generated with ID: {self.policy_id}")
                print(f"✅ Policy title: {response.get('title', 'No title')}")
                # Print first 100 chars of content to verify it's real
                content_preview = response.get('content', '')[:100] + '...' if len(response.get('content', '')) > 100 else response.get('content', '')
                print(f"✅ Policy content preview: {content_preview}")
            else:
                print("❌ No policy ID in response")
                success = False
        
        return success, response
        
    def test_get_specific_policy(self):
        """Test getting a specific policy"""
        if not self.policy_id:
            print("❌ No policy ID available to test")
            return False, {}
            
        return self.run_test(
            "Get Specific Policy",
            "GET",
            f"policies/{self.policy_id}",
            200
        )
        
    def test_create_project(self, company_name):
        """Test project creation"""
        return self.run_test(
            "Create Project",
            "POST",
            "projects",
            200,
            data={
                "framework": "gdpr",
                "company_name": company_name,
                "target_date": (datetime.now() + timedelta(days=30)).isoformat()
            }
        )
        
    def test_get_evidence_integrations(self):
        """Test getting evidence integrations"""
        return self.run_test(
            "Get Evidence Integrations",
            "GET",
            "evidence/integrations",
            200
        )

    def test_signup(self, fullName, companyName, email, password):
        """Test user signup"""
        signup_data = {
            "fullName": fullName,
            "companyName": companyName,
            "email": email,
            "password": password
        }
        
        print("\n🔍 Testing User Signup...")
        success, response = self.run_test(
            "User Signup",
            "POST",
            "auth/signup",
            200,
            data=signup_data
        )
        
        if success:
            # Check if response contains token and user data
            if 'token' in response and 'user' in response:
                # Verify token is a real JWT (starts with eyJ)
                if response['token'].startswith('eyJ'):
                    print("✅ Token is a real JWT")
                else:
                    print("❌ Token is not a real JWT")
                    success = False
                
                # Verify user data contains correct information
                user_data = response['user']
                if user_data.get('fullName') == fullName and \
                   user_data.get('companyName') == companyName and \
                   user_data.get('email') == email:
                    print("✅ User data contains correct information")
                    self.user_data = user_data
                else:
                    print("❌ User data does not match input")
                    success = False
                
                # Set token for subsequent requests
                self.auth_token = response['token']
            else:
                print("❌ Response missing token or user data")
                success = False
        
        return success, response

    def test_login(self, email, password):
        """Test user login"""
        login_data = {
            "email": email,
            "password": password
        }
        
        print("\n🔍 Testing User Login...")
        success, response = self.run_test(
            "User Login",
            "POST",
            "auth/login",
            200,
            data=login_data
        )
        
        if success:
            # Check if response contains token and user data
            if 'token' in response and 'user' in response:
                # Verify token is a real JWT (starts with eyJ)
                if response['token'].startswith('eyJ'):
                    print("✅ Token is a real JWT")
                else:
                    print("❌ Token is not a real JWT")
                    success = False
                
                # Verify user data contains correct information
                user_data = response['user']
                if user_data.get('email') == email:
                    print("✅ User data contains correct email")
                    self.user_data = user_data
                else:
                    print("❌ User data does not match input email")
                    success = False
                
                # Set token for subsequent requests
                self.auth_token = response['token']
            else:
                print("❌ Response missing token or user data")
                success = False
        
        return success, response
        
    def test_get_current_user(self):
        """Test getting current user from token"""
        return self.run_test(
            "Get Current User from Token",
            "GET",
            "auth/me",
            200
        )
        
    def test_logout_and_relogin(self, email, password):
        """Test logout and re-login to verify data persistence"""
        print("\n🔍 Testing Logout and Re-login...")
        
        # Clear token to simulate logout
        print("Simulating logout by clearing token...")
        old_token = self.auth_token
        self.auth_token = None
        
        # Try to access dashboard without token (should fail with 401)
        no_auth_success, _ = self.run_test(
            "Access Dashboard Without Auth",
            "GET",
            "dashboard",
            401
        )
        
        if no_auth_success:
            print("✅ Unauthenticated access correctly rejected")
        else:
            print("❌ Unauthenticated access not properly handled")
            
        # Re-login
        login_success, login_response = self.test_login(email, password)
        
        if login_success:
            print("✅ Re-login successful")
            # Verify token is different (session management)
            if self.auth_token != old_token:
                print("✅ New session token issued")
            else:
                print("❌ Same token reused")
                
            # Verify we can access dashboard again
            dashboard_success, dashboard_data = self.test_get_dashboard()
            if dashboard_success:
                print("✅ Dashboard access restored after re-login")
                
                # Check if policies are still there
                policies_success, policies_data = self.test_get_policies()
                if policies_success:
                    policy_count = len(policies_data.get('policies', []))
                    if policy_count > 0:
                        print(f"✅ User data persisted: {policy_count} policies found after re-login")
                    else:
                        print("❌ No policies found after re-login")
            else:
                print("❌ Dashboard access failed after re-login")
                login_success = False
        
        return login_success

def main():
    # Setup
    tester = ComplianceGPTTester()
    
    # Run tests
    print("\n===== TESTING BACKEND API ENDPOINTS =====")
    
    # Core functionality tests
    tester.test_health_check()
    tester.test_get_frameworks()
    tester.test_get_specific_framework()
    
    # Authentication tests
    print("\n===== TESTING AUTHENTICATION (REAL JWT AUTHENTICATION) =====")
    
    # Use the specific test user from the requirements
    test_fullname = "Maria Rodriguez"
    test_company = "SecureTech Solutions"
    test_email = "<EMAIL>"
    test_password = "secure2024pass"
    
    # Test signup
    signup_success, signup_response = tester.test_signup(
        test_fullname, 
        test_company, 
        test_email, 
        test_password
    )
    
    # If signup fails, try login (user might already exist)
    if not signup_success:
        print("Signup failed, trying login with same credentials (user might already exist)...")
        login_success, login_response = tester.test_login(test_email, test_password)
    else:
        login_success = True
    
    # Test current user endpoint
    if login_success:
        current_user_success, current_user_data = tester.test_get_current_user()
        if current_user_success:
            print("✅ Current user endpoint working")
        else:
            print("❌ Current user endpoint failed")
    
    # Test dashboard with authentication
    print("\n===== TESTING DASHBOARD WITH REAL USER DATA =====")
    if login_success:
        dashboard_success, dashboard_data = tester.test_get_dashboard()
    else:
        dashboard_success = False
        print("❌ Skipping dashboard test due to authentication failure")
    
    # Test policy generation with authentication
    print("\n===== TESTING POLICY GENERATION WITH REAL USER =====")
    if login_success:
        policy_success, policy_data = tester.test_generate_policy(test_company)
    else:
        policy_success = False
        print("❌ Skipping policy generation test due to authentication failure")
    
    # Test getting policies after generation
    print("\n===== TESTING POLICIES RETRIEVAL AFTER GENERATION =====")
    if policy_success:
        policies_success, policies_data = tester.test_get_policies()
        if policies_success:
            policy_count = len(policies_data.get('policies', []))
            if policy_count > 0:
                print(f"✅ Found {policy_count} policies for user")
            else:
                print("❌ No policies found for user")
    else:
        policies_success = False
        print("❌ Skipping policies retrieval test due to policy generation failure")
    
    # Test getting specific policy
    if policy_success:
        specific_policy_success, specific_policy_data = tester.test_get_specific_policy()
        if specific_policy_success:
            print(f"✅ Successfully retrieved specific policy: {specific_policy_data.get('title', 'No title')}")
    
    # Test logout and re-login to verify data persistence
    print("\n===== TESTING DATA PERSISTENCE ACROSS SESSIONS =====")
    if login_success:
        relogin_success = tester.test_logout_and_relogin(test_email, test_password)
    else:
        relogin_success = False
        print("❌ Skipping logout/re-login test due to initial authentication failure")
    
    # Print results
    print(f"\n📊 Tests passed: {tester.tests_passed}/{tester.tests_run}")
    
    # Specific feedback on the real data architecture
    print("\n===== REAL DATA ARCHITECTURE VALIDATION =====")
    
    if signup_success or login_success:
        print("✅ AUTHENTICATION: Real user authentication working")
        print(f"   - User: {test_fullname} from {test_company}")
        print("   - Real JWT tokens generated and validated")
    else:
        print("❌ AUTHENTICATION: Issues with real authentication flow")
    
    if dashboard_success:
        print("✅ DASHBOARD: Shows real user data")
        print(f"   - User information displayed correctly")
        print("   - Real-time stats and data shown")
    else:
        print("❌ DASHBOARD: Issues with real user data display")
    
    if policy_success:
        print("✅ POLICY GENERATION: Creates real policies linked to authenticated user")
        print("   - Policy saved to database with user association")
        print("   - Policy content is comprehensive and professional")
    else:
        print("❌ POLICY GENERATION: Issues with real policy creation")
    
    if relogin_success:
        print("✅ DATA PERSISTENCE: User data persists across sessions")
        print("   - Policies remain accessible after logout/login")
        print("   - User preferences and settings maintained")
    else:
        print("❌ DATA PERSISTENCE: Issues with data persistence")
    
    return 0 if tester.tests_passed == tester.tests_run else 1

if __name__ == "__main__":
    sys.exit(main())
