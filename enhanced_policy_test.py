#!/usr/bin/env python3
"""
ComplianceGPT Enhanced Policy Generator Test Script
Tests the enhanced policy generation functionality
"""

import requests
import json
import sys
import time
import uuid
from datetime import datetime

class EnhancedPolicyTester:
    def __init__(self, base_url="https://5fb85ea3-d551-4232-b367-a6d8d4edbcbe.preview.emergentagent.com"):
        self.base_url = base_url
        self.token = None
        self.user_id = None
        self.tests_run = 0
        self.tests_passed = 0
        self.test_results = []

    def run_test(self, name, method, endpoint, expected_status, data=None, headers=None):
        """Run a single API test"""
        url = f"{self.base_url}/api/{endpoint}"
        
        if headers is None:
            headers = {'Content-Type': 'application/json'}
        
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'

        self.tests_run += 1
        print(f"\n🔍 Testing {name}...")
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=headers)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers)
            
            success = response.status_code == expected_status
            
            if success:
                self.tests_passed += 1
                print(f"✅ Passed - Status: {response.status_code}")
                try:
                    return success, response.json()
                except:
                    return success, {}
            else:
                print(f"❌ Failed - Expected {expected_status}, got {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data.get('detail', 'Unknown error')}")
                    return False, error_data
                except:
                    print(f"Error: {response.text}")
                    return False, {}

        except Exception as e:
            print(f"❌ Failed - Error: {str(e)}")
            return False, {}

    def register_user(self):
        """Register a test user"""
        test_user_id = str(uuid.uuid4())[:8]
        user_data = {
            "email": f"test_user_{test_user_id}@example.com",
            "password": "TestPassword123!",
            "fullName": f"Test User {test_user_id}",
            "companyName": "Test Company"
        }
        
        success, response = self.run_test(
            "Register User",
            "POST",
            "auth/signup",
            200,
            data=user_data
        )
        
        if success and 'token' in response:
            self.token = response['token']
            self.user_id = response.get('id')
            self.test_results.append({
                "test": "User Registration",
                "success": True,
                "user_id": self.user_id,
                "email": user_data["email"]
            })
            return True
        
        return False

    def login_user(self, email, password):
        """Login with existing user"""
        login_data = {
            "email": email,
            "password": password
        }
        
        success, response = self.run_test(
            "Login User",
            "POST",
            "auth/login",
            200,
            data=login_data
        )
        
        if success and 'token' in response:
            self.token = response['token']
            self.user_id = response.get('id')
            self.test_results.append({
                "test": "User Login",
                "success": True,
                "user_id": self.user_id
            })
            return True
        
        return False

    def test_enhanced_policy_generation(self):
        """Test the enhanced policy generation endpoint"""
        if not self.token:
            print("❌ Authentication required for policy generation")
            return False
        
        policy_data = {
            "framework": "GDPR",
            "company_name": "InnovateSecure Technologies",
            "company_type": "Technology Company",
            "industry": "Technology",
            "employee_count": 125,
            "data_types": ["personal_data", "financial_data", "customer_data"],
            "existing_policies": [],
            "complexity_level": "comprehensive",
            "jurisdiction": "UK",
            "risk_profile": "medium",
            "generation_mode": "enhanced"
        }
        
        print("\n🔍 Testing Enhanced Policy Generation...")
        print("This may take up to 2 minutes to complete...")
        
        start_time = time.time()
        
        success, response = self.run_test(
            "Generate Enhanced Policy",
            "POST",
            "policies/generate-enhanced",
            200,
            data=policy_data
        )
        
        generation_time = time.time() - start_time
        
        if success:
            policy_id = response.get('policy_id')
            metadata = response.get('metadata', {})
            quality_metrics = response.get('quality_metrics', {})
            
            # Validate policy metrics
            total_words = metadata.get('total_words', 0)
            total_pages = metadata.get('total_pages', 0)
            overall_score = quality_metrics.get('overall_score', 0)
            
            print(f"\n📊 Enhanced Policy Generation Results:")
            print(f"  Policy ID: {policy_id}")
            print(f"  Total Words: {total_words:,}")
            print(f"  Total Pages: {total_pages}")
            print(f"  Quality Score: {overall_score:.1%}")
            print(f"  Generation Time: {generation_time:.1f} seconds")
            
            # Validate against requirements
            meets_word_count = total_words >= 15000
            meets_page_count = total_pages >= 20
            meets_quality_score = overall_score >= 0.9
            meets_time_requirement = generation_time <= 120
            
            success_criteria = {
                "Word Count (15,000+)": meets_word_count,
                "Page Count (20+)": meets_page_count,
                "Quality Score (90%+)": meets_quality_score,
                "Generation Time (<120s)": meets_time_requirement
            }
            
            print("\n🎯 Success Criteria:")
            for criterion, result in success_criteria.items():
                status = "✅ Passed" if result else "❌ Failed"
                print(f"  {criterion}: {status}")
            
            overall_success = all(success_criteria.values())
            
            self.test_results.append({
                "test": "Enhanced Policy Generation",
                "success": overall_success,
                "policy_id": policy_id,
                "metrics": {
                    "total_words": total_words,
                    "total_pages": total_pages,
                    "quality_score": overall_score,
                    "generation_time": generation_time
                },
                "success_criteria": success_criteria
            })
            
            # Test retrieving the generated policy
            if policy_id:
                self.test_get_enhanced_policy(policy_id)
            
            return overall_success
        
        return False

    def test_get_enhanced_policy(self, policy_id):
        """Test retrieving an enhanced policy"""
        success, response = self.run_test(
            "Get Enhanced Policy",
            "GET",
            f"policies/{policy_id}/enhanced",
            200
        )
        
        if success:
            # Check if policy has all required sections
            content_sections = response.get('content_sections', {})
            has_framework = 'framework' in content_sections
            has_procedures = 'procedures' in content_sections
            has_tools = 'tools' in content_sections
            has_assembled = 'assembled' in content_sections
            
            sections_complete = all([has_framework, has_procedures, has_tools, has_assembled])
            
            print("\n📑 Policy Structure Validation:")
            print(f"  Framework Section: {'✅ Present' if has_framework else '❌ Missing'}")
            print(f"  Procedures Section: {'✅ Present' if has_procedures else '❌ Missing'}")
            print(f"  Tools Section: {'✅ Present' if has_tools else '❌ Missing'}")
            print(f"  Assembled Document: {'✅ Present' if has_assembled else '❌ Missing'}")
            
            self.test_results.append({
                "test": "Get Enhanced Policy",
                "success": sections_complete,
                "policy_id": policy_id,
                "sections_validation": {
                    "has_framework": has_framework,
                    "has_procedures": has_procedures,
                    "has_tools": has_tools,
                    "has_assembled": has_assembled
                }
            })
            
            return sections_complete
        
        return False

    def test_export_policy(self, policy_id):
        """Test policy export functionality"""
        success, _ = self.run_test(
            "Export Policy (Markdown)",
            "GET",
            f"policies/{policy_id}/export/comprehensive?format=markdown",
            200,
            headers={'Accept': 'text/markdown'}
        )
        
        if success:
            self.test_results.append({
                "test": "Export Policy",
                "success": True,
                "policy_id": policy_id,
                "format": "markdown"
            })
            return True
        
        return False

    def test_enhanced_analytics(self):
        """Test enhanced analytics endpoint"""
        success, response = self.run_test(
            "Get Enhanced Analytics",
            "GET",
            "policies/enhanced/analytics",
            200
        )
        
        if success:
            analytics = response.get('analytics', {})
            has_required_fields = all(field in analytics for field in [
                'total_policies', 'average_quality_score', 'average_word_count',
                'framework_breakdown', 'quality_trends'
            ])
            
            self.test_results.append({
                "test": "Enhanced Analytics",
                "success": has_required_fields,
                "analytics_data": analytics
            })
            
            return has_required_fields
        
        return False

    def run_all_tests(self):
        """Run all API tests"""
        print("\n🚀 Starting ComplianceGPT Enhanced API Tests")
        print("=" * 60)
        
        # Step 1: Register a test user
        if not self.register_user():
            print("❌ User registration failed, stopping tests")
            return False
        
        # Step 2: Test enhanced policy generation
        if not self.test_enhanced_policy_generation():
            print("⚠️ Enhanced policy generation did not meet all criteria")
        
        # Step 3: Test analytics
        self.test_enhanced_analytics()
        
        # Print test summary
        print("\n📊 Test Summary:")
        print(f"  Tests Run: {self.tests_run}")
        print(f"  Tests Passed: {self.tests_passed}")
        print(f"  Success Rate: {(self.tests_passed / self.tests_run) * 100:.1f}%")
        
        return self.tests_passed == self.tests_run

def main():
    # Get base URL from command line if provided
    base_url = sys.argv[1] if len(sys.argv) > 1 else "https://5fb85ea3-d551-4232-b367-a6d8d4edbcbe.preview.emergentagent.com"
    
    tester = EnhancedPolicyTester(base_url)
    success = tester.run_all_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())