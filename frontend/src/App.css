@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom styles for ComplianceGPT */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom component styles */
.compliance-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 transition-shadow hover:shadow-md;
}

.compliance-button {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors;
}

.compliance-button:disabled {
  @apply bg-gray-400 cursor-not-allowed;
}

.compliance-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.compliance-select {
  @apply block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.framework-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.framework-badge.gdpr {
  @apply bg-blue-100 text-blue-800;
}

.framework-badge.soc2 {
  @apply bg-green-100 text-green-800;
}

.framework-badge.iso27001 {
  @apply bg-purple-100 text-purple-800;
}

.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-badge.planning {
  @apply bg-yellow-100 text-yellow-800;
}

.status-badge.implementing {
  @apply bg-blue-100 text-blue-800;
}

.status-badge.ready {
  @apply bg-green-100 text-green-800;
}

.status-badge.draft {
  @apply bg-gray-100 text-gray-800;
}

/* Progress bar animations */
.progress-bar {
  @apply transition-all duration-300 ease-in-out;
}

/* Loading animations */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom scrollbar for policy content */
.policy-content::-webkit-scrollbar {
  width: 8px;
}

.policy-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.policy-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.policy-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Responsive typography */
@media (max-width: 640px) {
  .text-2xl {
    @apply text-xl;
  }
  
  .text-lg {
    @apply text-base;
  }
}

/* Focus states for accessibility */
.focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Animation for successful generation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* Custom gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Policy generation form improvements */
.form-section {
  @apply space-y-6 p-6 bg-white rounded-lg border border-gray-200;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-help {
  @apply text-xs text-gray-500;
}

/* Dashboard card hover effects */
.dashboard-card {
  @apply transition-all duration-200 hover:shadow-lg hover:scale-105;
}

/* Mobile responsiveness improvements */
@media (max-width: 768px) {
  .mobile-stack {
    @apply flex-col space-y-4;
  }
  
  .mobile-full {
    @apply w-full;
  }
}

/* Dark mode support (for future enhancement) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }
}