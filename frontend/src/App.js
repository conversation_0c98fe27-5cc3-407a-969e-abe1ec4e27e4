import React, { useState, useEffect } from 'react';
import './App.css';
import { Dashboard, PolicyGenerator, Policies, Projects, Integrations, Evidence, AIAssistant, AuditorPortal, PricingPage, EnhancedPolicyGenerator } from './components';
import HeroPage from './components/HeroPage';
import { API_BASE, FRAMEWORK_COLORS, STATUS_COLORS } from './utils/constants';

// Icons as inline SVG components
const CheckIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
);

const ClockIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const ShieldIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
);

const DocumentIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
);

const SparklesIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
  </svg>
);

const TrendingUpIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
  </svg>
);

function App() {
  // App state management
  const [showHeroPage, setShowHeroPage] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [frameworks, setFrameworks] = useState([]);
  const [policies, setPolicies] = useState([]);
  const [projects, setProjects] = useState([]);

  // Check for existing authentication on app load
  useEffect(() => {
    const savedUser = localStorage.getItem('complianceGPT-user');
    const savedToken = localStorage.getItem('complianceGPT-token');
    
    if (savedUser && savedToken) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        setShowHeroPage(false); // Skip hero page if already authenticated
      } catch (error) {
        console.error('Error loading saved user:', error);
        localStorage.removeItem('complianceGPT-user');
        localStorage.removeItem('complianceGPT-token');
      }
    }
  }, []);

  // Initialize dark mode from localStorage
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('complianceGPT-darkMode');
    if (savedDarkMode) {
      setIsDarkMode(JSON.parse(savedDarkMode));
    }
  }, []);

  // Save dark mode preference
  useEffect(() => {
    localStorage.setItem('complianceGPT-darkMode', JSON.stringify(isDarkMode));
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  const handleAuthentication = (userData) => {
    setUser(userData);
    setShowHeroPage(false);
    // Save to localStorage for persistence (token is already saved in AuthModal)
    localStorage.setItem('complianceGPT-user', JSON.stringify(userData));
  };

  const handleUserUpgrade = (upgradedUser) => {
    setUser(upgradedUser);
    localStorage.setItem('complianceGPT-user', JSON.stringify(upgradedUser));
  };

  const logout = () => {
    localStorage.removeItem('complianceGPT-user');
    localStorage.removeItem('complianceGPT-token');
    setUser(null);
    setShowHeroPage(true);
  };

  const enterApp = () => {
    if (user) {
      setShowHeroPage(false);
    } else {
      // Redirect to signup if not authenticated
      setShowHeroPage(true);
    }
  };
  
  // Policy generation state
  const [policyForm, setPolicyForm] = useState({
    framework: '',
    company_name: '',
    company_type: '',
    employee_count: 50,
    industry: '',
    data_types: [],
    existing_policies: [],
    complexity_level: 'standard'
  });
  const [generatingPolicy, setGeneratingPolicy] = useState(false);
  const [generatedPolicy, setGeneratedPolicy] = useState(null);

  // Project creation state
  const [projectForm, setProjectForm] = useState({
    framework: '',
    company_name: '',
    target_date: ''
  });
  const [creatingProject, setCreatingProject] = useState(false);

  // Check authentication status on app load
  useEffect(() => {
    const checkAuthStatus = () => {
      const token = localStorage.getItem('complianceGPT-token');
      const userData = localStorage.getItem('complianceGPT-user');
      
      if (token && userData) {
        try {
          const user = JSON.parse(userData);
          setUser(user);
          setShowHeroPage(false);
          // Only fetch data after confirming user is authenticated
          setTimeout(() => {
            fetchFrameworks();
            fetchDashboardData();
          }, 100);
        } catch (error) {
          console.error('Error parsing user data:', error);
          // Clear invalid data and show hero page
          localStorage.removeItem('complianceGPT-token');
          localStorage.removeItem('complianceGPT-user');
          setShowHeroPage(true);
        }
      } else {
        // No authentication, show hero page
        setShowHeroPage(true);
      }
      setLoading(false);
    };

    checkAuthStatus();
  }, []);

  useEffect(() => {
    console.log('Frameworks loaded:', frameworks);
    console.log('Policy form state:', policyForm);
  }, [frameworks, policyForm]);

  // Helper function to make authenticated API calls
  const makeAuthenticatedRequest = async (url, options = {}) => {
    const token = localStorage.getItem('complianceGPT-token');
    const backendUrl = process.env.REACT_APP_BACKEND_URL;
    
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await fetch(`${backendUrl}${url}`, {
      ...options,
      headers
    });
    
    if (!response.ok) {
      throw new Error(`API call failed: ${response.status}`);
    }
    
    return response.json();
  };

  const fetchDashboardData = async () => {
    try {
      const data = await makeAuthenticatedRequest('/api/dashboard');
      setDashboardData(data);
      setPolicies(data.recent_policies || []);
      setProjects(data.recent_projects || []);
      console.log('Real dashboard data loaded:', data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Set empty arrays for graceful fallback
      setPolicies([]);
      setProjects([]);
      // If authentication error, redirect to hero page
      if (error.message.includes('401') || error.message.includes('Authorization')) {
        console.log('Authentication error detected, redirecting to hero page');
        localStorage.removeItem('complianceGPT-token');
        localStorage.removeItem('complianceGPT-user');
        setUser(null);
        setShowHeroPage(true);
      }
    }
  };

  const fetchFrameworks = async () => {
    try {
      const data = await makeAuthenticatedRequest('/api/frameworks');
      setFrameworks(data.frameworks || []);
      console.log('Frameworks loaded:', data.frameworks?.length || 0);
    } catch (error) {
      console.error('Error fetching frameworks:', error);
      setFrameworks([]);
    }
  };

  // Policy export function
  const exportPolicy = async (policyId, format) => {
    try {
      const response = await fetch(`${API_BASE}/api/policies/${policyId}/export/${format}`, {
        method: 'GET'
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `policy.${format === 'word' ? 'docx' : 'pdf'}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      console.error('Export error:', error);
      alert('Failed to export policy. Please try again.');
    }
  };

  // Enhanced Generate Policy function
  const generatePolicy = async (enhancedFormData = null, isEnhanced = false) => {
    const formData = enhancedFormData || policyForm;
    
    console.log('Policy form state:', formData);
    console.log('Available frameworks:', frameworks);
    
    if (!formData.framework || !formData.company_name || !formData.industry) {
      alert('Please fill in required fields: Framework, Company Name, and Industry');
      return;
    }

    setGeneratingPolicy(true);
    try {
      console.log('Sending policy generation request:', formData);
      
      const requestData = {
        ...formData,
        generation_mode: isEnhanced ? 'enhanced' : 'legacy'
      };
      
      const data = await makeAuthenticatedRequest('/api/policies/generate', {
        method: 'POST',
        body: JSON.stringify(requestData)
      });

      console.log('Policy generated successfully:', data);
      setGeneratedPolicy(data);
      
      // Refresh policies list
      fetchDashboardData();
      
    } catch (error) {
      console.error('Error generating policy:', error);
      alert(`Error generating policy: ${error.message}`);
    } finally {
      setGeneratingPolicy(false);
    }
  };

  const createProject = async () => {
    if (!projectForm.framework || !projectForm.company_name) {
      alert('Please fill in required fields: Framework and Company Name');
      return;
    }

    setCreatingProject(true);
    try {
      const queryParams = new URLSearchParams({
        framework: projectForm.framework,
        company_name: projectForm.company_name,
        ...(projectForm.target_date && { target_date: projectForm.target_date })
      });

      const response = await fetch(`${API_BASE}/api/projects?${queryParams}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Reset form and refresh data
      setProjectForm({ framework: '', company_name: '', target_date: '' });
      fetchDashboardData();
      
      alert('Project created successfully!');
      
    } catch (error) {
      console.error('Error creating project:', error);
      alert(`Error creating project: ${error.message}`);
    } finally {
      setCreatingProject(false);
    }
  };

  const formatProgress = (progress) => {
    return Math.min(Math.max(progress || 0, 0), 100);
  };

  const getFrameworkColor = (framework) => {
    return FRAMEWORK_COLORS[framework] || 'bg-gray-500';
  };

  const getStatusColor = (status) => {
    return STATUS_COLORS[status] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show Hero Page if not entered app yet
  if (showHeroPage) {
    return (
      <HeroPage 
        onEnterApp={enterApp}
        toggleDarkMode={toggleDarkMode}
        isDarkMode={isDarkMode}
        onAuthenticate={handleAuthentication}
      />
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gray-900 text-white' 
        : 'bg-gray-50 text-gray-900'
    }`}>
      <div className={`border-b transition-colors duration-300 ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                ComplianceGPT
              </h1>
              <div className="ml-2 px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                AI-Powered
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* User Info */}
              {user && (
                <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Welcome, {user.fullName}
                </div>
              )}
              
              {/* Dark Mode Toggle */}
              <button
                onClick={toggleDarkMode}
                className={`p-2 rounded-lg transition-colors duration-300 ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}
                title={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}
              >
                {isDarkMode ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                )}
              </button>

              {/* Logout Button */}
              {user && (
                <button
                  onClick={logout}
                  className={`px-4 py-2 rounded-lg transition-colors duration-300 ${
                    isDarkMode 
                      ? 'text-gray-300 hover:text-white hover:bg-gray-700' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Sign Out
                </button>
              )}
            </div>
          </div>

          <nav className="space-x-1 pb-4">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'dashboard'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode 
                    ? 'text-gray-300 hover:text-blue-400 hover:bg-gray-700'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100'
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('policy-generator')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'policy-generator'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode 
                    ? 'text-gray-300 hover:text-blue-400 hover:bg-gray-700'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100'
              }`}
            >
              AI Policy Generator
            </button>
            <button
              onClick={() => setActiveTab('policies')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'policies'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode 
                    ? 'text-gray-300 hover:text-blue-400 hover:bg-gray-700'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100'
              }`}
            >
              Policies
            </button>
            <button
              onClick={() => setActiveTab('projects')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'projects'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode 
                    ? 'text-gray-300 hover:text-blue-400 hover:bg-gray-700'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100'
              }`}
            >
              Projects
            </button>
            <button
              onClick={() => setActiveTab('integrations')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'integrations'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode 
                    ? 'text-gray-300 hover:text-blue-400 hover:bg-gray-700'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100'
              }`}
            >
              Integrations
            </button>
            <button
              onClick={() => setActiveTab('evidence')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'evidence'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode 
                    ? 'text-gray-300 hover:text-blue-400 hover:bg-gray-700'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100'
              }`}
            >
              Evidence
            </button>
            <button
              onClick={() => setActiveTab('pricing')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'pricing'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode 
                    ? 'text-gray-300 hover:text-blue-400 hover:bg-gray-700'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100'
              }`}
            >
              Pricing
            </button>
            <button
              onClick={() => setActiveTab('auditor-portal')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'auditor-portal'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode 
                    ? 'text-gray-300 hover:text-blue-400 hover:bg-gray-700'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-100'
              }`}
            >
              Auditor Portal
            </button>
            </nav>
          </div>
        </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <Dashboard 
            dashboardData={dashboardData}
            frameworks={frameworks}
            policies={policies}
            projects={projects}
            loading={loading}
            isDarkMode={isDarkMode}
            getFrameworkColor={getFrameworkColor}
            getStatusColor={getStatusColor}
            formatProgress={formatProgress}
          />
        )}

        {/* AI Policy Generator Tab */}
        {activeTab === 'ai-assistant' && (
          <EnhancedPolicyGenerator
            isDarkMode={isDarkMode}
            generatePolicy={generatePolicy}
            loading={generatingPolicy}
            error={generatedPolicy?.error}
            generatedPolicy={generatedPolicy}
            policyForm={policyForm}
            setPolicyForm={setPolicyForm}
          />
        )}

        {/* Policies Tab */}
        {activeTab === 'policies' && (
          <Policies 
            policies={policies}
            isDarkMode={isDarkMode}
          />
        )}

        {/* Projects Tab */}
        {activeTab === 'projects' && (
          <Projects 
            projects={projects}
            isDarkMode={isDarkMode}
          />
        )}

        {/* Integrations Tab */}
        {activeTab === 'integrations' && (
          <Integrations 
            backendUrl={API_BASE}
            isDarkMode={isDarkMode}
          />
        )}

        {/* Evidence Tab */}
        {activeTab === 'evidence' && (
          <Evidence 
            backendUrl={API_BASE}
            isDarkMode={isDarkMode}
          />
        )}

        {/* Pricing Tab */}
        {activeTab === 'pricing' && (
          <PricingPage 
            isDarkMode={isDarkMode}
            user={user}
            onUpgrade={handleUserUpgrade}
          />
        )}

        {/* Auditor Portal Tab */}
        {activeTab === 'auditor-portal' && (
          <AuditorPortal 
            backendUrl={API_BASE}
            isDarkMode={isDarkMode}
          />
        )}
      </main>
    </div>
  );
}

export default App;