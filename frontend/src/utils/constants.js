// Constants for ComplianceGPT application

export const API_BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';

export const FRAMEWORK_COLORS = {
  gdpr: 'bg-blue-500',
  soc2: 'bg-green-500',
  iso27001: 'bg-purple-500'
};

export const STATUS_COLORS = {
  planning: 'bg-yellow-100 text-yellow-800',
  implementing: 'bg-blue-100 text-blue-800',
  ready: 'bg-green-100 text-green-800',
  draft: 'bg-gray-100 text-gray-800',
  review: 'bg-orange-100 text-orange-800',
  approved: 'bg-green-100 text-green-800',
  archived: 'bg-gray-100 text-gray-800'
};

export const COMPANY_TYPES = [
  'SaaS',
  'Fintech', 
  'E-commerce',
  'Healthcare',
  'Consulting',
  'Other'
];

export const DATA_TYPES = [
  'Personal Data',
  'Financial Data', 
  'Health Data',
  'Biometric Data',
  'Location Data'
];

export const COMPLEXITY_LEVELS = [
  { value: 'basic', label: 'Basic (Fast Mode)' },
  { value: 'standard', label: 'Standard (Balanced)' },
  { value: 'advanced', label: 'Advanced (Thinking Mode)' }
];