import React, { useState } from 'react';

const DemoModal = ({ isOpen, onClose, isDarkMode }) => {
  const [demoType, setDemoType] = useState('book'); // 'book' or 'interactive'
  const [bookingData, setBookingData] = useState({
    fullName: '',
    email: '',
    companyName: '',
    phone: '',
    companySize: '',
    preferredTime: '',
    message: ''
  });
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [demoRunning, setDemoRunning] = useState(false);
  const [demoAnimation, setDemoAnimation] = useState('');
  const [showResult, setShowResult] = useState(false);

  const interactiveDemoSteps = [
    {
      title: "AI Policy Generation Demo",
      description: "See how ComplianceGPT generates a professional GDPR policy in real-time",
      content: "Our AI analyzes your company profile and generates audit-ready compliance policies. Watch as we create a comprehensive Data Protection Policy with all required GDPR sections.",
      action: "▶ Generate Policy",
      demoAnimation: "Generating GDPR Data Protection Policy...\n✓ Analyzing company requirements\n✓ Mapping GDPR articles\n✓ Creating policy sections\n✓ Adding legal references",
      result: "✅ 16-page GDPR Data Protection Policy generated!\nIncludes: Lawful basis, Data subject rights, Privacy by design, Breach procedures"
    },
    {
      title: "Evidence Collection Demo",
      description: "Experience automated compliance evidence gathering from real business tools",
      content: "Connect your Google Workspace, Slack, GitHub and other tools for automatic evidence collection. See live data being pulled and analyzed.",
      action: "▶ Collect Evidence",
      demoAnimation: "Connecting to business tools...\n✓ Google Workspace: Admin logs, User access\n✓ Slack: Security settings, User policies\n✓ GitHub: Code security, Access controls\n✓ Analyzing compliance evidence",
      result: "✅ 47 pieces of compliance evidence collected!\nCoverage: User access (100%), Security settings (95%), Admin logs (100%)"
    },
    {
      title: "Compliance Dashboard Demo",
      description: "Monitor your real-time compliance status with professional dashboards",
      content: "Track progress across GDPR, SOC 2, and ISO 27001 with visual indicators, traffic lights, and automated compliance scoring.",
      action: "▶ View Dashboard",
      demoAnimation: "Building compliance dashboard...\n✓ GDPR compliance: 85% complete\n✓ SOC 2 readiness: 72% complete\n✓ ISO 27001 progress: 68% complete\n✓ Traffic light indicators updated",
      result: "✅ Compliance dashboard ready!\nOverall score: 75% | 23 controls complete | 8 in progress | 4 pending"
    },
    {
      title: "Audit Package Demo",
      description: "Generate professional audit documentation with one click",
      content: "Create comprehensive audit packages with all evidence, policies, and documentation ready for external auditors.",
      action: "▶ Generate Package",
      demoAnimation: "Preparing audit package...\n✓ Compiling 16 policies\n✓ Organizing 47 evidence items\n✓ Creating executive summary\n✓ Generating PDF documentation",
      result: "✅ Complete audit package generated!\n67-page comprehensive audit documentation with all required evidence and policies"
    }
  ];

  const handleBookingSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Store booking for follow-up (in real app, this would go to CRM)
      const booking = {
        ...bookingData,
        submittedAt: new Date().toISOString(),
        id: Math.random().toString(36).substr(2, 9)
      };
      
      localStorage.setItem('complianceGPT-demo-booking', JSON.stringify(booking));
      setSubmitted(true);
    } catch (error) {
      console.error('Demo booking error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setBookingData({
      ...bookingData,
      [e.target.name]: e.target.value
    });
  };

  const runDemo = async () => {
    const currentStepData = interactiveDemoSteps[currentStep];
    setDemoRunning(true);
    setShowResult(false);
    setDemoAnimation('');

    // Simulate realistic demo animation
    const animationLines = currentStepData.demoAnimation.split('\n');
    
    for (let i = 0; i < animationLines.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 800)); // Realistic timing
      setDemoAnimation(prev => prev + (i > 0 ? '\n' : '') + animationLines[i]);
    }

    // Show final result
    await new Promise(resolve => setTimeout(resolve, 1000));
    setShowResult(true);
    setDemoRunning(false);
  };

  const nextStep = () => {
    if (currentStep < interactiveDemoSteps.length - 1) {
      setCurrentStep(currentStep + 1);
      setDemoAnimation('');
      setShowResult(false);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      setDemoAnimation('');
      setShowResult(false);
    }
  };

  const resetDemo = () => {
    setCurrentStep(0);
    setSubmitted(false);
    setDemoType('book');
    setDemoRunning(false);
    setDemoAnimation('');
    setShowResult(false);
    setBookingData({
      fullName: '',
      email: '',
      companyName: '',
      phone: '',
      companySize: '',
      preferredTime: '',
      message: ''
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`max-w-2xl w-full rounded-lg shadow-xl transition-colors duration-300 max-h-[90vh] overflow-y-auto ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            ComplianceGPT Demo
          </h2>
          <button
            onClick={() => {
              resetDemo();
              onClose();
            }}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode 
                ? 'hover:bg-gray-700 text-gray-400 hover:text-white' 
                : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
            }`}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Demo Type Selection */}
        {!submitted && (
          <div className="p-6">
            <div className="flex space-x-4 mb-6">
              <button
                onClick={() => setDemoType('interactive')}
                className={`flex-1 p-4 rounded-lg border-2 transition-all duration-300 ${
                  demoType === 'interactive'
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : isDarkMode 
                      ? 'border-gray-600 hover:border-gray-500' 
                      : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-2xl mb-2">🎮</div>
                <h3 className={`font-semibold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Interactive Demo
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Explore the platform yourself
                </p>
              </button>

              <button
                onClick={() => setDemoType('book')}
                className={`flex-1 p-4 rounded-lg border-2 transition-all duration-300 ${
                  demoType === 'book'
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : isDarkMode 
                      ? 'border-gray-600 hover:border-gray-500' 
                      : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-2xl mb-2">📅</div>
                <h3 className={`font-semibold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Book a Demo
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Schedule with our team
                </p>
              </button>
            </div>

            {/* Interactive Demo */}
            {demoType === 'interactive' && (
              <div>
                <div className={`rounded-lg p-6 mb-6 ${
                  isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
                }`}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {interactiveDemoSteps[currentStep].title}
                    </h3>
                    <span className={`text-sm px-3 py-1 rounded-full ${
                      isDarkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                    }`}>
                      Step {currentStep + 1} of {interactiveDemoSteps.length}
                    </span>
                  </div>
                  
                  <p className={`text-lg mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {interactiveDemoSteps[currentStep].description}
                  </p>
                  
                  <p className={`mb-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {interactiveDemoSteps[currentStep].content}
                  </p>

                  <div className="flex items-center justify-between">
                    <button
                      onClick={prevStep}
                      disabled={currentStep === 0}
                      className={`px-4 py-2 rounded-md transition-colors ${
                        currentStep === 0
                          ? 'opacity-50 cursor-not-allowed'
                          : isDarkMode
                            ? 'bg-gray-600 hover:bg-gray-500 text-white'
                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                      }`}
                    >
                      ← Previous
                    </button>

                    <button
                      onClick={runDemo}
                      disabled={demoRunning}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-md hover:from-blue-700 hover:to-purple-700 transition-all duration-300 disabled:opacity-50"
                    >
                      {demoRunning ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Running...
                        </div>
                      ) : (
                        interactiveDemoSteps[currentStep].action
                      )}
                    </button>

                    <button
                      onClick={nextStep}
                      disabled={currentStep === interactiveDemoSteps.length - 1}
                      className={`px-4 py-2 rounded-md transition-colors ${
                        currentStep === interactiveDemoSteps.length - 1
                          ? 'opacity-50 cursor-not-allowed'
                          : isDarkMode
                            ? 'bg-gray-600 hover:bg-gray-500 text-white'
                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                      }`}
                    >
                      Next →
                    </button>
                  </div>

                  {/* Demo Animation Display */}
                  {(demoAnimation || showResult) && (
                    <div className={`mt-6 p-4 rounded-lg font-mono text-sm ${
                      isDarkMode ? 'bg-gray-900 border border-gray-600' : 'bg-gray-100 border border-gray-300'
                    }`}>
                      {demoAnimation && (
                        <div className={`mb-4 ${isDarkMode ? 'text-green-300' : 'text-green-700'}`}>
                          <pre className="whitespace-pre-wrap">{demoAnimation}</pre>
                        </div>
                      )}
                      
                      {showResult && (
                        <div className={`p-4 rounded-lg ${
                          isDarkMode ? 'bg-green-900/20 border border-green-700' : 'bg-green-50 border border-green-200'
                        }`}>
                          <p className={`font-medium ${isDarkMode ? 'text-green-300' : 'text-green-800'}`}>
                            <pre className="whitespace-pre-wrap">{interactiveDemoSteps[currentStep].result}</pre>
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Progress Indicator */}
                  <div className={`mt-6 p-3 rounded-lg ${
                    isDarkMode ? 'bg-gray-700' : 'bg-blue-50'
                  }`}>
                    <div className="flex justify-between text-sm mb-2">
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
                        Demo Progress
                      </span>
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
                        {currentStep + 1} of {interactiveDemoSteps.length}
                      </span>
                    </div>
                    <div className={`w-full bg-gray-200 rounded-full h-2 ${isDarkMode ? 'bg-gray-600' : ''}`}>
                      <div
                        className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${((currentStep + 1) / interactiveDemoSteps.length) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                {currentStep === interactiveDemoSteps.length - 1 && (
                  <div className="text-center">
                    <p className={`mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Ready to get started with ComplianceGPT?
                    </p>
                    <button
                      onClick={() => {
                        onClose();
                        // This would trigger the signup modal
                      }}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 text-lg font-semibold"
                    >
                      Start Your Free Trial
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Book Demo Form */}
            {demoType === 'book' && (
              <form onSubmit={handleBookingSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Full Name *
                    </label>
                    <input
                      type="text"
                      name="fullName"
                      value={bookingData.fullName}
                      onChange={handleInputChange}
                      required
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="Your full name"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={bookingData.email}
                      onChange={handleInputChange}
                      required
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Company Name *
                    </label>
                    <input
                      type="text"
                      name="companyName"
                      value={bookingData.companyName}
                      onChange={handleInputChange}
                      required
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="Your company"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={bookingData.phone}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="+44 20 1234 5678"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Company Size
                    </label>
                    <select
                      name="companySize"
                      value={bookingData.companySize}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white' 
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="">Select size</option>
                      <option value="10-25">10-25 employees</option>
                      <option value="26-50">26-50 employees</option>
                      <option value="51-100">51-100 employees</option>
                      <option value="101-200">101-200 employees</option>
                      <option value="200+">200+ employees</option>
                    </select>
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Preferred Time
                    </label>
                    <select
                      name="preferredTime"
                      value={bookingData.preferredTime}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white' 
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="">Select time</option>
                      <option value="morning">Morning (9-12 GMT)</option>
                      <option value="afternoon">Afternoon (12-17 GMT)</option>
                      <option value="flexible">Flexible</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Message (Optional)
                  </label>
                  <textarea
                    name="message"
                    value={bookingData.message}
                    onChange={handleInputChange}
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    placeholder="Tell us about your compliance needs..."
                  />
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-md hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 transition-all duration-300 flex items-center justify-center"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  ) : null}
                  {loading ? 'Booking Demo...' : 'Book Your Demo'}
                </button>
              </form>
            )}
          </div>
        )}

        {/* Success Message */}
        {submitted && (
          <div className="p-6 text-center">
            <div className="text-6xl mb-4">🎉</div>
            <h3 className={`text-2xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Demo Booked Successfully!
            </h3>
            <p className={`mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Thank you for your interest in ComplianceGPT. Our team will contact you within 24 hours to schedule your personalized demo.
            </p>
            <div className={`p-4 rounded-lg mb-6 ${
              isDarkMode ? 'bg-blue-900/20 border border-blue-700' : 'bg-blue-50 border border-blue-200'
            }`}>
              <p className={`text-sm ${isDarkMode ? 'text-blue-300' : 'text-blue-800'}`}>
                <strong>What happens next:</strong>
              </p>
              <ul className={`text-sm mt-2 space-y-1 ${isDarkMode ? 'text-blue-200' : 'text-blue-700'}`}>
                <li>• Our compliance expert will contact you within 24 hours</li>
                <li>• We'll schedule a 30-minute personalized demo</li>
                <li>• See how ComplianceGPT can save your company £50K+</li>
                <li>• Get answers to your specific compliance questions</li>
              </ul>
            </div>
            <button
              onClick={() => {
                resetDemo();
                onClose();
              }}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-md hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
            >
              Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DemoModal;