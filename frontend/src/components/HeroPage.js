import React, { useState } from 'react';
import AuthModal from './AuthModal';
import DemoModal from './DemoModal';
import PricingModal from './PricingModal';

const HeroPage = ({ onEnterApp, toggleDarkMode, isDarkMode, onAuthenticate }) => {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showDemoModal, setShowDemoModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [authMode, setAuthMode] = useState('signup'); // 'login' or 'signup'
  const features = [
    {
      icon: '🤖',
      title: 'AI-Powered Policy Generation',
      description: 'Generate professional compliance policies in minutes using Gemini 2.5 Flash AI technology.'
    },
    {
      icon: '🔗',
      title: '25+ Business Integrations',
      description: 'Connect all your business tools for automated evidence collection and compliance monitoring.'
    },
    {
      icon: '📊',
      title: 'Real-time Compliance Dashboard',
      description: 'Track your compliance progress with intuitive dashboards and traffic light indicators.'
    },
    {
      icon: '🔒',
      title: 'Secure Auditor Portal',
      description: 'Share compliance evidence securely with external auditors through time-limited access.'
    },
    {
      icon: '📄',
      title: 'Professional Documentation',
      description: 'Export branded PDF and Word documents ready for audit submission.'
    },
    {
      icon: '⚡',
      title: 'Automated Evidence Collection',
      description: 'Collect compliance evidence automatically from integrated tools with screenshot capture.'
    }
  ];

  const benefits = [
    { metric: '95%', label: 'Time Reduction', description: 'From 200+ hours to under 20 hours annually' },
    { metric: '£50K+', label: 'Cost Savings', description: 'Replace expensive compliance consultants' },
    { metric: '7 Days', label: 'Time to Compliance', description: 'Achieve compliance in days, not months' },
    { metric: '3', label: 'Frameworks Supported', description: 'GDPR, SOC 2, and ISO 27001' }
  ];

  const testimonials = [
    {
      name: 'Sarah Mitchell',
      role: 'Head of Operations',
      company: 'TechCorp Ltd',
      quote: 'ComplianceGPT helped us achieve SOC 2 compliance in just 5 days. We closed 3 enterprise deals that we would have lost without it.',
      avatar: '👩‍💼'
    },
    {
      name: 'James Rodriguez',
      role: 'Technical Co-founder',
      company: 'FinanceFlow',
      quote: 'The AI policy generation is incredible. Professional, audit-ready policies that would have taken weeks to create manually.',
      avatar: '👨‍💻'
    },
    {
      name: 'Rachel Thompson',
      role: 'Compliance Consultant',
      company: 'Compliance Partners',
      quote: 'I use ComplianceGPT for all my clients. It has transformed my consultancy and allowed me to scale efficiently.',
      avatar: '👩‍⚖️'
    }
  ];

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gray-900 text-white' 
        : 'bg-gradient-to-br from-blue-50 via-white to-purple-50 text-gray-900'
    }`}>
      {/* Header */}
      <header className={`border-b transition-colors duration-300 ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white/80'
      } backdrop-blur-sm sticky top-0 z-50`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                ComplianceGPT
              </div>
              <div className="ml-2 px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                AI-Powered
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Dark Mode Toggle */}
              <button
                onClick={toggleDarkMode}
                className={`p-2 rounded-lg transition-colors duration-300 ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}
                title={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}
              >
                {isDarkMode ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                )}
              </button>
              
              {/* Login Button */}
              <button
                onClick={() => {
                  setAuthMode('login');
                  setShowAuthModal(true);
                }}
                className={`px-4 py-2 rounded-lg transition-colors duration-300 ${
                  isDarkMode 
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Sign In
              </button>

              <button
                onClick={() => setShowPricingModal(true)}
                className={`px-4 py-2 rounded-lg transition-colors duration-300 ${
                  isDarkMode 
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Pricing
              </button>
              
              <button
                onClick={() => {
                  setAuthMode('signup');
                  setShowAuthModal(true);
                }}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                Start Free Trial
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              AI Compliance Automation
            </span>
            <br />
            <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>
              for UK SMBs
            </span>
          </h1>
          
          <p className={`text-xl md:text-2xl mb-8 max-w-3xl mx-auto ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Achieve GDPR, SOC 2, and ISO 27001 compliance in <strong>days, not months</strong>. 
            Replace £50K consultants with intelligent automation.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <button
              onClick={() => {
                setAuthMode('signup');
                setShowAuthModal(true);
              }}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              🚀 Start Free Trial
            </button>
            <button 
              onClick={() => setShowDemoModal(true)}
              className={`px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 border-2 ${
                isDarkMode 
                  ? 'border-gray-600 text-gray-300 hover:border-gray-500 hover:bg-gray-800' 
                  : 'border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50'
              }`}
            >
              📖 View Demo
            </button>
          </div>

          {/* Benefits Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            {benefits.map((benefit, index) => (
              <div key={index} className={`text-center p-6 rounded-xl transition-all duration-300 ${
                isDarkMode 
                  ? 'bg-gray-800 border border-gray-700 hover:bg-gray-750' 
                  : 'bg-white border border-gray-200 hover:shadow-lg'
              }`}>
                <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                  {benefit.metric}
                </div>
                <div className={`font-semibold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {benefit.label}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {benefit.description}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className={`py-20 ${
        isDarkMode ? 'bg-gray-800' : 'bg-gray-50'
      } transition-colors duration-300`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className={`text-4xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Everything You Need for Compliance
            </h2>
            <p className={`text-xl ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Professional-grade compliance automation designed specifically for UK SMBs
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className={`p-8 rounded-xl transition-all duration-300 hover:transform hover:scale-105 ${
                isDarkMode 
                  ? 'bg-gray-900 border border-gray-700 hover:border-gray-600' 
                  : 'bg-white border border-gray-200 hover:shadow-xl'
              }`}>
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className={`text-xl font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {feature.title}
                </h3>
                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className={`text-4xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Trusted by UK SMBs
            </h2>
            <p className={`text-xl ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              See how ComplianceGPT transforms compliance for growing businesses
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className={`p-8 rounded-xl transition-all duration-300 ${
                isDarkMode 
                  ? 'bg-gray-800 border border-gray-700' 
                  : 'bg-white border border-gray-200 shadow-lg'
              }`}>
                <div className="flex items-center mb-4">
                  <div className="text-3xl mr-4">{testimonial.avatar}</div>
                  <div>
                    <div className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {testimonial.name}
                    </div>
                    <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {testimonial.role} at {testimonial.company}
                    </div>
                  </div>
                </div>
                <p className={`italic ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  "{testimonial.quote}"
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className={`py-20 ${
        isDarkMode ? 'bg-gray-800' : 'bg-gradient-to-r from-blue-600 to-purple-600'
      } transition-colors duration-300`}>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Transform Your Compliance?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join hundreds of UK SMBs who have already automated their compliance with ComplianceGPT
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => {
                setAuthMode('signup');
                setShowAuthModal(true);
              }}
              className={`px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 ${
                isDarkMode 
                  ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                  : 'bg-white text-blue-600 hover:bg-gray-50'
              }`}
            >
              🚀 Start Free Trial
            </button>
            <button
              onClick={() => setShowDemoModal(true)}
              className={`px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 border-2 border-white/20 text-white hover:bg-white/10`}
            >
              📖 Book a Demo
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className={`py-12 border-t transition-colors duration-300 ${
        isDarkMode 
          ? 'border-gray-700 bg-gray-900' 
          : 'border-gray-200 bg-white'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <div className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mr-4">
                ComplianceGPT
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                AI-Powered Compliance for UK SMBs
              </div>
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              © 2025 ComplianceGPT. Democratizing enterprise-grade compliance.
            </div>
          </div>
        </div>
      </footer>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
        onSwitchMode={setAuthMode}
        onAuthenticate={onAuthenticate}
        isDarkMode={isDarkMode}
      />

      {/* Demo Modal */}
      <DemoModal
        isOpen={showDemoModal}
        onClose={() => setShowDemoModal(false)}
        isDarkMode={isDarkMode}
      />

      {/* Pricing Modal */}
      <PricingModal
        isOpen={showPricingModal}
        onClose={() => setShowPricingModal(false)}
        isDarkMode={isDarkMode}
        onSignUp={(selectedPlan) => {
          setAuthMode('signup');
          setShowAuthModal(true);
          // You could pass the selected plan to the auth modal if needed
        }}
      />
    </div>
  );
};

export default HeroPage;