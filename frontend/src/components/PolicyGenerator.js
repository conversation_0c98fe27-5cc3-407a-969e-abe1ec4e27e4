import React from 'react';

const PolicyGenerator = ({ 
  policyForm, 
  setPolicyForm, 
  frameworks, 
  generatePolicy, 
  generatingPolicy, 
  generatedPolicy,
  exportPolicy
}) => {
  const SparklesIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
    </svg>
  );

  const CheckIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
  );

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <SparklesIcon />
            <h2 className="ml-2 text-lg font-medium text-gray-900">AI Policy Generator</h2>
            <span className="ml-2 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
              Powered by Gemini 2.5 Flash
            </span>
          </div>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Company Information */}
            <div className="space-y-4">
              <h3 className="text-md font-semibold text-gray-900">Company Information</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Name *
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={policyForm.company_name}
                  onChange={(e) => setPolicyForm({...policyForm, company_name: e.target.value})}
                  placeholder="e.g., TechCorp Ltd"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Type
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={policyForm.company_type}
                  onChange={(e) => setPolicyForm({...policyForm, company_type: e.target.value})}
                >
                  <option value="">Select type</option>
                  <option value="SaaS">SaaS Company</option>
                  <option value="Fintech">Fintech</option>
                  <option value="E-commerce">E-commerce</option>
                  <option value="Healthcare">Healthcare</option>
                  <option value="Consulting">Consulting</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Industry *
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={policyForm.industry}
                  onChange={(e) => setPolicyForm({...policyForm, industry: e.target.value})}
                  placeholder="e.g., Software Development, Financial Services"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employee Count
                </label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={policyForm.employee_count}
                  onChange={(e) => setPolicyForm({...policyForm, employee_count: parseInt(e.target.value)})}
                  min="1"
                  max="1000"
                />
              </div>
            </div>

            {/* Compliance Configuration */}
            <div className="space-y-4">
              <h3 className="text-md font-semibold text-gray-900">Compliance Configuration</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Framework *
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={policyForm.framework}
                  onChange={(e) => setPolicyForm({...policyForm, framework: e.target.value})}
                >
                  <option value="">Select framework</option>
                  {frameworks.map((framework) => (
                    <option key={framework.id} value={framework.id}>
                      {framework.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data Types Processed
                </label>
                <div className="space-y-2">
                  {['Personal Data', 'Financial Data', 'Health Data', 'Biometric Data', 'Location Data'].map((type) => (
                    <label key={type} className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        checked={policyForm.data_types.includes(type)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setPolicyForm({...policyForm, data_types: [...policyForm.data_types, type]});
                          } else {
                            setPolicyForm({...policyForm, data_types: policyForm.data_types.filter(t => t !== type)});
                          }
                        }}
                      />
                      <span className="ml-2 text-sm text-gray-700">{type}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Complexity Level
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={policyForm.complexity_level}
                  onChange={(e) => setPolicyForm({...policyForm, complexity_level: e.target.value})}
                >
                  <option value="basic">Basic (Fast Mode)</option>
                  <option value="standard">Standard (Balanced)</option>
                  <option value="advanced">Advanced (Thinking Mode)</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Advanced mode uses Gemini's thinking capabilities for complex scenarios
                </p>
              </div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={generatePolicy}
              disabled={generatingPolicy || !policyForm.framework || !policyForm.company_name || !policyForm.industry}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
            >
              {generatingPolicy ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Generating with Gemini AI...
                </>
              ) : (
                <>
                  <SparklesIcon />
                  <span className="ml-2">Generate AI Policy</span>
                </>
              )}
            </button>
          </div>

          {/* Generated Policy Display */}
          {generatedPolicy && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <div className="flex items-center">
                  <CheckIcon />
                  <span className="ml-2 text-green-800 font-medium">
                    Policy Generated Successfully!
                  </span>
                  {generatedPolicy.thinking_mode && (
                    <span className="ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">
                      Thinking Mode
                    </span>
                  )}
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {generatedPolicy.title}
                </h3>
                
                {/* Export Buttons */}
                <div className="flex space-x-2 mb-4">
                  <button
                    onClick={() => exportPolicy(generatedPolicy.policy_id, 'pdf')}
                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors flex items-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Export PDF
                  </button>
                  <button
                    onClick={() => exportPolicy(generatedPolicy.policy_id, 'word')}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Export Word
                  </button>
                </div>
                
                <div className="bg-white rounded border p-4 max-h-96 overflow-y-auto policy-content">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700">
                    {generatedPolicy.content}
                  </pre>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PolicyGenerator;