import React, { useState, useEffect } from 'react';

const Integrations = ({ 
  backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001'
}) => {
  const [integrations, setIntegrations] = useState([]);
  const [integrationsByCategory, setIntegrationsByCategory] = useState({});
  const [loading, setLoading] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  const [integrationStatus, setIntegrationStatus] = useState(null);

  const fetchExtendedIntegrations = async () => {
    try {
      const response = await fetch(`${backendUrl}/api/integrations/all`);
      if (response.ok) {
        const data = await response.json();
        setIntegrationsByCategory(data.integrations_by_category);
        setIntegrationStatus(data.status);
      }
    } catch (error) {
      console.error('Error fetching extended integrations:', error);
    }
  };

  const setupIntegration = async (integrationId) => {
    if (!integrationId) return;
    
    setLoading(true);
    try {
      // Mock credentials for demo - in production, this would be a proper OAuth flow
      const mockCredentials = {
        api_key: `demo_${integrationId}_key`,
        setup_time: new Date().toISOString()
      };

      const response = await fetch(`${backendUrl}/api/integrations/${integrationId}/setup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mockCredentials)
      });

      if (response.ok) {
        const result = await response.json();
        alert(`${result.integration.name} connected successfully!`);
        fetchExtendedIntegrations(); // Refresh the list
      } else {
        throw new Error('Setup failed');
      }
    } catch (error) {
      console.error('Error setting up integration:', error);
      alert('Failed to setup integration. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const syncAllIntegrations = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${backendUrl}/api/integrations/sync-all`, {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        alert(result.message);
        fetchExtendedIntegrations(); // Refresh status
      } else {
        throw new Error('Sync failed');
      }
    } catch (error) {
      console.error('Error syncing integrations:', error);
      alert('Failed to sync integrations. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getCategoryIcon = (category) => {
    const icons = {
      'productivity': '💼',
      'communication': '💬',
      'development': '💻',
      'cloud': '☁️',
      'project-management': '📋',
      'documentation': '📚',
      'security': '🔒',
      'crm': '👥',
      'payments': '💳',
      'design': '🎨',
      'identity': '🆔',
      'monitoring': '📊',
      'customer-support': '🎧',
      'marketing': '📢',
      'email': '📧'
    };
    return icons[category] || '🔧';
  };

  const getCategoryColor = (category) => {
    const colors = {
      'productivity': 'bg-blue-100 text-blue-800',
      'communication': 'bg-green-100 text-green-800',
      'development': 'bg-purple-100 text-purple-800',
      'cloud': 'bg-cyan-100 text-cyan-800',
      'project-management': 'bg-orange-100 text-orange-800',
      'documentation': 'bg-yellow-100 text-yellow-800',
      'security': 'bg-red-100 text-red-800',
      'crm': 'bg-pink-100 text-pink-800',
      'payments': 'bg-emerald-100 text-emerald-800',
      'design': 'bg-violet-100 text-violet-800',
      'identity': 'bg-indigo-100 text-indigo-800',
      'monitoring': 'bg-gray-100 text-gray-800',
      'customer-support': 'bg-teal-100 text-teal-800',
      'marketing': 'bg-rose-100 text-rose-800',
      'email': 'bg-lime-100 text-lime-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  useEffect(() => {
    fetchExtendedIntegrations();
  }, []);

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Integrations Marketplace</h1>
            <p className="text-gray-600">
              Connect your business tools for automated compliance evidence collection
            </p>
          </div>
          
          {/* Sync All Button */}
          <button
            onClick={syncAllIntegrations}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            )}
            Sync All Integrations
          </button>
        </div>

        {/* Integration Status Summary */}
        {integrationStatus && (
          <div className="bg-white rounded-lg shadow-md p-4 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{integrationStatus.total_available}</div>
                <div className="text-sm text-gray-600">Available Tools</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{integrationStatus.total_connected}</div>
                <div className="text-sm text-gray-600">Connected</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{integrationStatus.connection_rate}%</div>
                <div className="text-sm text-gray-600">Connection Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{Object.keys(integrationsByCategory).length}</div>
                <div className="text-sm text-gray-600">Categories</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Integration Categories */}
      <div className="space-y-8">
        {Object.entries(integrationsByCategory).map(([category, categoryIntegrations]) => (
          <div key={category} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center">
                <span className="text-2xl mr-3">{getCategoryIcon(category)}</span>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 capitalize">
                    {category.replace('-', ' ')}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {categoryIntegrations.length} tools available
                  </p>
                </div>
                <span className={`ml-auto px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(category)}`}>
                  {category.replace('-', ' ').toUpperCase()}
                </span>
              </div>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {categoryIntegrations.map((integration) => (
                  <div key={integration.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-gray-900">{integration.name}</h3>
                      <div className={`w-3 h-3 rounded-full ${
                        integration.status === 'connected' ? 'bg-green-500' : 
                        integration.status === 'available' ? 'bg-gray-400' : 'bg-yellow-500'
                      }`}></div>
                    </div>
                    
                    <div className="mb-3">
                      <div className="text-xs text-gray-500 uppercase font-semibold mb-1">Evidence Types</div>
                      <div className="flex flex-wrap gap-1">
                        {integration.evidence_types.slice(0, 2).map((type, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                            {type}
                          </span>
                        ))}
                        {integration.evidence_types.length > 2 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                            +{integration.evidence_types.length - 2} more
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <button
                      onClick={() => setupIntegration(integration.id)}
                      disabled={loading || integration.status === 'connected'}
                      className={`w-full py-2 px-3 rounded text-sm font-medium transition-colors ${
                        integration.status === 'connected'
                          ? 'bg-green-100 text-green-700 cursor-not-allowed'
                          : 'bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50'
                      }`}
                    >
                      {integration.status === 'connected' ? 'Connected' : 'Connect'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Integration Modal would go here if needed */}
    </div>
  );
};

export default Integrations;