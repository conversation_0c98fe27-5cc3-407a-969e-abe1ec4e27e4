import React, { useState } from 'react';

const PricingModal = ({ isOpen, onClose, isDarkMode, onSignUp }) => {
  const [billingCycle, setBillingCycle] = useState('monthly');

  const plans = [
    {
      id: 'starter',
      name: 'Starter',
      description: 'Perfect for small teams getting started',
      monthlyPrice: 99,
      annualPrice: 990,
      features: [
        '1 compliance framework',
        'Up to 5 AI-generated policies',
        '3 business tool integrations',
        'Basic compliance dashboard',
        'Email support',
        'Up to 25 employees'
      ],
      popular: false
    },
    {
      id: 'professional',
      name: 'Professional',
      description: 'Most popular for growing UK SMBs',
      monthlyPrice: 249,
      annualPrice: 2490,
      features: [
        'All 3 compliance frameworks',
        'Unlimited AI-generated policies',
        '15+ business tool integrations',
        'Advanced dashboard with traffic lights',
        'Automated evidence collection',
        'PDF/Word exports with branding',
        'Priority support',
        'Up to 100 employees'
      ],
      popular: true
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'For larger teams with advanced needs',
      monthlyPrice: 499,
      annualPrice: 4990,
      features: [
        'Everything in Professional',
        '25+ business tool integrations',
        'Automated screenshot capture',
        'Secure auditor portal',
        'Advanced version control',
        'White-label reporting',
        'Dedicated success manager',
        'Phone support + SLA',
        'Up to 200 employees'
      ],
      popular: false
    }
  ];

  const getPrice = (plan) => {
    return billingCycle === 'monthly' ? plan.monthlyPrice : Math.floor(plan.annualPrice / 12);
  };

  const getSavings = (plan) => {
    const monthlyTotal = plan.monthlyPrice * 12;
    return monthlyTotal - plan.annualPrice;
  };

  const handleSelectPlan = (plan) => {
    if (plan.id === 'enterprise') {
      window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank');
      return;
    }
    
    // Close modal and trigger signup with selected plan
    onClose();
    if (onSignUp) {
      onSignUp(plan.id);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`max-w-6xl w-full rounded-lg shadow-xl transition-colors duration-300 max-h-[90vh] overflow-y-auto ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Simple, Transparent Pricing
              </span>
            </h2>
            <p className={`text-lg mt-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Choose the perfect plan for your compliance automation needs
            </p>
          </div>
          <button
            onClick={onClose}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode 
                ? 'hover:bg-gray-700 text-gray-400 hover:text-white' 
                : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
            }`}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6">
          {/* ROI Message */}
          <div className="text-center mb-8">
            <div className={`inline-flex items-center px-4 py-2 rounded-lg ${
              isDarkMode ? 'bg-green-900/20 text-green-300' : 'bg-green-50 text-green-600'
            }`}>
              <span className="text-2xl mr-2">💰</span>
              <span className="font-semibold">Save £50,000+ vs hiring compliance consultants</span>
            </div>
          </div>

          {/* Billing Toggle */}
          <div className="flex justify-center mb-8">
            <div className={`inline-flex p-1 rounded-lg border ${
              isDarkMode ? 'border-gray-700 bg-gray-700' : 'border-gray-200 bg-gray-100'
            }`}>
              <button
                onClick={() => setBillingCycle('monthly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingCycle === 'monthly'
                    ? 'bg-blue-600 text-white'
                    : isDarkMode
                      ? 'text-gray-300 hover:text-white'
                      : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingCycle('annual')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors relative ${
                  billingCycle === 'annual'
                    ? 'bg-blue-600 text-white'
                    : isDarkMode
                      ? 'text-gray-300 hover:text-white'
                      : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Annual
                <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1 rounded">
                  Save 17%
                </span>
              </button>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`relative rounded-xl border transition-all duration-300 hover:scale-105 ${
                  plan.popular
                    ? 'border-blue-500 shadow-lg'
                    : isDarkMode
                      ? 'border-gray-600 hover:border-gray-500'
                      : 'border-gray-200 hover:border-gray-300'
                } ${isDarkMode ? 'bg-gray-700' : 'bg-white'}`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-full text-sm font-semibold shadow-lg z-10">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className="p-6">
                  {/* Plan Header */}
                  <div className="text-center mb-6">
                    <h3 className={`text-xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {plan.name}
                    </h3>
                    <p className={`text-sm mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {plan.description}
                    </p>
                    
                    {/* Price */}
                    <div className="mb-4">
                      <span className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        £{getPrice(plan)}
                      </span>
                      <span className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        /month
                      </span>
                      {billingCycle === 'annual' && (
                        <div className="text-sm text-green-600 mt-1">
                          Save £{getSavings(plan)}/year
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Features */}
                  <ul className="space-y-2 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <button
                    onClick={() => handleSelectPlan(plan)}
                    className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 ${
                      plan.popular
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                        : plan.id === 'enterprise'
                          ? isDarkMode
                            ? 'bg-gray-600 text-white hover:bg-gray-500'
                            : 'bg-gray-800 text-white hover:bg-gray-700'
                          : isDarkMode
                            ? 'bg-gray-600 text-white hover:bg-gray-500'
                            : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                    }`}
                  >
                    {plan.id === 'enterprise' ? 'Contact Sales' : 'Start Free Trial'}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Value Proposition */}
          <div className={`mt-8 p-6 rounded-xl ${
            isDarkMode ? 'bg-gray-700' : 'bg-blue-50'
          }`}>
            <h3 className={`text-xl font-bold text-center mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Why ComplianceGPT?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl mb-2">⚡</div>
                <h4 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>95% Faster</h4>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Generate policies in minutes
                </p>
              </div>
              <div>
                <div className="text-2xl mb-2">💰</div>
                <h4 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>£50K+ Savings</h4>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Replace expensive consultants
                </p>
              </div>
              <div>
                <div className="text-2xl mb-2">🤖</div>
                <h4 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>AI-Powered</h4>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Latest Gemini 2.5 Flash
                </p>
              </div>
              <div>
                <div className="text-2xl mb-2">🇬🇧</div>
                <h4 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>UK-Focused</h4>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Built for UK SMBs
                </p>
              </div>
            </div>
          </div>

          {/* Free Trial Info */}
          <div className={`mt-6 p-4 rounded-lg ${
            isDarkMode ? 'bg-green-900/20 border border-green-700' : 'bg-green-50 border border-green-200'
          }`}>
            <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-green-300' : 'text-green-800'}`}>
              🎯 14-Day Free Trial Includes:
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div className={`text-sm ${isDarkMode ? 'text-green-200' : 'text-green-700'}`}>
                ✓ Generate 5 AI-powered policies
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-green-200' : 'text-green-700'}`}>
                ✓ Connect 3 business integrations
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-green-200' : 'text-green-700'}`}>
                ✓ Access all compliance frameworks
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-green-200' : 'text-green-700'}`}>
                ✓ No credit card required
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingModal;