import React, { useState, useEffect } from 'react';
import { API_BASE } from '../utils/constants';

const Evidence = () => {
  const [evidence, setEvidence] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    framework: '',
    evidence_type: '',
    integration: ''
  });
  const [gapAnalysis, setGapAnalysis] = useState(null);

  // Icons
  const EvidenceIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  );

  const AnalyticsIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );

  const AlertIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
    </svg>
  );

  const CheckCircleIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );

  useEffect(() => {
    fetchEvidence();
  }, [filters]);

  const fetchEvidence = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.framework) params.append('framework', filters.framework);
      if (filters.evidence_type) params.append('evidence_type', filters.evidence_type);
      if (filters.integration) params.append('integration', filters.integration);
      params.append('limit', '50');

      const response = await fetch(`${API_BASE}/api/evidence/evidence?${params}`);
      const data = await response.json();
      setEvidence(data.evidence || []);
    } catch (error) {
      console.error('Error fetching evidence:', error);
    } finally {
      setLoading(false);
    }
  };

  const analyzeGaps = async (framework) => {
    try {
      const response = await fetch(`${API_BASE}/api/evidence/evidence/analysis/gaps?framework=${framework}`);
      const data = await response.json();
      setGapAnalysis(data);
    } catch (error) {
      console.error('Error analyzing gaps:', error);
    }
  };

  const getFrameworkColor = (framework) => {
    const colors = {
      gdpr: 'bg-blue-100 text-blue-800',
      soc2: 'bg-green-100 text-green-800',
      iso27001: 'bg-purple-100 text-purple-800'
    };
    return colors[framework] || 'bg-gray-100 text-gray-800';
  };

  const getEvidenceTypeColor = (type) => {
    const colors = {
      user_access: 'bg-orange-100 text-orange-800',
      admin_logs: 'bg-red-100 text-red-800',
      security_settings: 'bg-indigo-100 text-indigo-800',
      data_access: 'bg-yellow-100 text-yellow-800',
      infrastructure: 'bg-green-100 text-green-800',
      code_security: 'bg-purple-100 text-purple-800',
      policy_enforcement: 'bg-blue-100 text-blue-800',
      audit_trail: 'bg-gray-100 text-gray-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Evidence Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <EvidenceIcon />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Evidence</p>
              <p className="text-2xl font-semibold text-gray-900">{evidence.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <AnalyticsIcon />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Evidence Types</p>
              <p className="text-2xl font-semibold text-gray-900">
                {new Set(evidence.map(e => e.evidence_type)).size}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <CheckCircleIcon />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Fresh Evidence</p>
              <p className="text-2xl font-semibold text-gray-900">
                {evidence.filter(e => {
                  const daysOld = (new Date() - new Date(e.collected_at)) / (1000 * 60 * 60 * 24);
                  return daysOld <= 30;
                }).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <AlertIcon />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Integrations</p>
              <p className="text-2xl font-semibold text-gray-900">
                {new Set(evidence.map(e => e.integration)).size}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Filter Evidence</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Framework</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filters.framework}
              onChange={(e) => setFilters({...filters, framework: e.target.value})}
            >
              <option value="">All Frameworks</option>
              <option value="gdpr">GDPR</option>
              <option value="soc2">SOC 2</option>
              <option value="iso27001">ISO 27001</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Evidence Type</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filters.evidence_type}
              onChange={(e) => setFilters({...filters, evidence_type: e.target.value})}
            >
              <option value="">All Types</option>
              <option value="user_access">User Access</option>
              <option value="admin_logs">Admin Logs</option>
              <option value="security_settings">Security Settings</option>
              <option value="data_access">Data Access</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="code_security">Code Security</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Integration</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filters.integration}
              onChange={(e) => setFilters({...filters, integration: e.target.value})}
            >
              <option value="">All Integrations</option>
              <option value="google_workspace">Google Workspace</option>
              <option value="microsoft365">Microsoft 365</option>
              <option value="slack">Slack</option>
              <option value="github">GitHub</option>
              <option value="aws">AWS</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setFilters({ framework: '', evidence_type: '', integration: '' })}
              className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Gap Analysis */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">Compliance Gap Analysis</h2>
          <div className="space-x-2">
            {['gdpr', 'soc2', 'iso27001'].map((framework) => (
              <button
                key={framework}
                onClick={() => analyzeGaps(framework)}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
              >
                Analyze {framework.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        {gapAnalysis && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-md font-semibold text-gray-900 mb-3">
              {gapAnalysis.framework.toUpperCase()} Analysis
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  {gapAnalysis.evidence_summary?.coverage_percentage || 0}%
                </p>
                <p className="text-sm text-gray-600">Coverage</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {gapAnalysis.evidence_summary?.evidence_types_covered || 0}
                </p>
                <p className="text-sm text-gray-600">Types Covered</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">
                  {gapAnalysis.gaps?.missing_evidence_types?.length || 0}
                </p>
                <p className="text-sm text-gray-600">Missing Types</p>
              </div>
            </div>

            {gapAnalysis.recommendations && gapAnalysis.recommendations.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Recommendations:</h4>
                <ul className="list-disc list-inside space-y-1">
                  {gapAnalysis.recommendations.map((rec, index) => (
                    <li key={index} className="text-sm text-gray-600">{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Evidence List */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Evidence Collection</h2>
          <p className="text-sm text-gray-500">
            Showing {evidence.length} evidence items collected automatically from integrated tools
          </p>
        </div>
        
        <div className="p-6">
          {evidence.length > 0 ? (
            <div className="space-y-4">
              {evidence.map((item) => (
                <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">{item.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{item.description}</p>
                      
                      <div className="flex flex-wrap gap-2 mb-2">
                        <span className={`px-2 py-1 text-xs rounded-full ${getEvidenceTypeColor(item.evidence_type)}`}>
                          {item.evidence_type.replace('_', ' ').toUpperCase()}
                        </span>
                        
                        {item.compliance_frameworks.map((framework) => (
                          <span key={framework} className={`px-2 py-1 text-xs rounded-full ${getFrameworkColor(framework)}`}>
                            {framework.toUpperCase()}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="text-right text-sm text-gray-500">
                      <p>{item.integration}</p>
                      <p>{formatDate(item.collected_at)}</p>
                    </div>
                  </div>

                  {/* Evidence Data Preview */}
                  <div className="bg-gray-50 rounded p-3 mt-3">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Evidence Summary:</h4>
                    <div className="text-sm text-gray-600">
                      {Object.entries(item.data).slice(0, 3).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <span className="font-medium">{key.replace('_', ' ')}:</span>
                          <span>{typeof value === 'object' ? JSON.stringify(value).slice(0, 50) + '...' : value}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Control Mappings */}
                  {item.control_mappings && Object.keys(item.control_mappings).length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Control Mappings:</h4>
                      <div className="space-y-1">
                        {Object.entries(item.control_mappings).map(([framework, control]) => (
                          <div key={framework} className="text-sm">
                            <span className="font-medium text-gray-700">{framework.toUpperCase()}:</span>
                            <span className="text-gray-600 ml-2">{control}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <EvidenceIcon />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No evidence collected</h3>
              <p className="mt-1 text-sm text-gray-500">
                Set up integrations to start collecting compliance evidence automatically.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => window.location.hash = '#integrations'}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  Setup Integrations
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Evidence;