import React, { useState, useEffect } from 'react';

const EnhancedPolicyGenerator = ({ isDarkMode, generatePolicy, loading, error, generatedPolicy, policyForm, setPolicyForm }) => {
  const [generationStage, setGenerationStage] = useState(null);
  const [progress, setProgress] = useState(0);
  const [stageDetails, setStageDetails] = useState({});
  const [enhancedMode, setEnhancedMode] = useState(true);
  const [formValidation, setFormValidation] = useState({});

  const stages = [
    { 
      id: 'framework', 
      name: 'Legal Framework', 
      duration: 30, 
      description: 'Generating comprehensive legal structure with regulatory mapping...',
      targetWords: '4,000-5,000',
      targetPages: '4-5'
    },
    { 
      id: 'procedures', 
      name: 'Implementation Procedures', 
      duration: 45, 
      description: 'Creating detailed operational workflows and step-by-step procedures...',
      targetWords: '10,000-12,000',
      targetPages: '10-12'
    },
    { 
      id: 'tools', 
      name: 'Tools & Templates', 
      duration: 30, 
      description: 'Building practical compliance tools, forms, and checklists...',
      targetWords: '6,000-8,000',
      targetPages: '6-8'
    },
    { 
      id: 'assembly', 
      name: 'Document Assembly', 
      duration: 15, 
      description: 'Assembling comprehensive policy document with professional formatting...',
      targetWords: '20,000+',
      targetPages: '20+'
    }
  ];

  const validateForm = () => {
    const errors = {};
    
    if (!policyForm.company_name || policyForm.company_name.length < 2) {
      errors.company_name = 'Company name must be at least 2 characters';
    }
    
    if (!policyForm.framework) {
      errors.framework = 'Please select a compliance framework';
    }
    
    if (!policyForm.industry) {
      errors.industry = 'Please select your industry';
    }
    
    if (!policyForm.employee_count || policyForm.employee_count < 1) {
      errors.employee_count = 'Employee count must be at least 1';
    }
    
    if (!policyForm.data_types || policyForm.data_types.length === 0) {
      errors.data_types = 'Please select at least one data type';
    }
    
    setFormValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleEnhancedGeneration = async () => {
    if (!validateForm()) {
      return;
    }

    const enhancedFormData = {
      ...policyForm,
      generation_mode: 'enhanced',
      complexity_level: 'comprehensive',
      jurisdiction: 'UK',
      risk_profile: policyForm.risk_profile || 'medium'
    };

    // Start generation with progress tracking
    setGenerationStage('framework');
    setProgress(0);
    
    try {
      // Call the existing generatePolicy function but with enhanced mode
      await generatePolicy(enhancedFormData, true); // Pass enhanced flag
    } catch (error) {
      console.error('Enhanced generation failed:', error);
      setGenerationStage(null);
      setProgress(0);
    }
  };

  const MultiStageProgressIndicator = () => {
    if (!generationStage) return null;

    const currentStageIndex = stages.findIndex(stage => stage.id === generationStage);
    const progressPercentage = ((currentStageIndex + 1) / stages.length) * 100;

    return (
      <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Generating Enterprise-Grade Policy
            </h3>
            <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              {Math.round(progressPercentage)}% Complete
            </span>
          </div>
          
          <div className={`w-full bg-gray-200 rounded-full h-2 ${isDarkMode ? 'bg-gray-700' : ''}`}>
            <div 
              className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        <div className="space-y-4">
          {stages.map((stage, index) => {
            const isCurrent = stage.id === generationStage;
            const isCompleted = index < currentStageIndex;
            const isPending = index > currentStageIndex;

            return (
              <div 
                key={stage.id} 
                className={`flex items-center p-3 rounded-lg border ${
                  isCurrent 
                    ? isDarkMode ? 'bg-blue-900/20 border-blue-600' : 'bg-blue-50 border-blue-300'
                    : isCompleted
                    ? isDarkMode ? 'bg-green-900/20 border-green-600' : 'bg-green-50 border-green-300'
                    : isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                  isCurrent 
                    ? 'bg-blue-600 text-white animate-pulse'
                    : isCompleted
                    ? 'bg-green-600 text-white'
                    : isDarkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-300 text-gray-600'
                }`}>
                  {isCompleted ? '✓' : isCurrent ? '⟳' : index + 1}
                </div>

                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {stage.name}
                      </h4>
                      <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        {stage.description}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        Target: {stage.targetPages} pages
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {stage.targetWords} words
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        ~{stage.duration}s
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {generationStage && (
          <div className={`mt-4 p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <div className="flex items-center justify-between">
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Estimated completion time: {stages.reduce((sum, stage, index) => {
                  return index >= currentStageIndex ? sum + stage.duration : sum;
                }, 0)} seconds
              </span>
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Generating...
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const EnhancedCompanyProfileForm = () => {
    const frameworks = [
      { value: 'GDPR', label: 'GDPR (General Data Protection Regulation)', description: 'EU data protection compliance' },
      { value: 'SOC2', label: 'SOC 2 (Service Organization Control 2)', description: 'Security and availability controls' },
      { value: 'ISO27001', label: 'ISO 27001 (Information Security Management)', description: 'International security standard' }
    ];

    const industries = [
      'Technology', 'Financial Services', 'Healthcare', 'E-commerce', 'Education', 
      'Manufacturing', 'Professional Services', 'Non-profit', 'Government', 'Other'
    ];

    const dataTypes = [
      'personal_data', 'financial_data', 'health_data', 'customer_data', 'employee_data',
      'marketing_data', 'analytics_data', 'transaction_data', 'location_data', 'biometric_data'
    ];

    const riskProfiles = [
      { value: 'low', label: 'Low Risk', description: 'Basic data processing, low compliance requirements' },
      { value: 'medium', label: 'Medium Risk', description: 'Standard business operations, moderate compliance' },
      { value: 'high', label: 'High Risk', description: 'Sensitive data, strict regulatory requirements' }
    ];

    return (
      <div className="space-y-6">
        {/* Generation Mode Toggle */}
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <h3 className={`text-lg font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Policy Generation Mode
          </h3>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                checked={enhancedMode}
                onChange={() => setEnhancedMode(true)}
                className="mr-2"
              />
              <div>
                <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Enhanced Mode (Recommended)
                </span>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Generate comprehensive 20+ page enterprise-grade policies with detailed procedures and tools
                </p>
              </div>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                checked={!enhancedMode}
                onChange={() => setEnhancedMode(false)}
                className="mr-2"
              />
              <div>
                <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Legacy Mode
                </span>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Generate basic policy outlines (8-10 pages)
                </p>
              </div>
            </label>
          </div>
        </div>

        {/* Company Information */}
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <h3 className={`text-lg font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Company Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Company Name *
              </label>
              <input
                type="text"
                value={policyForm.company_name || ''}
                onChange={(e) => setPolicyForm({...policyForm, company_name: e.target.value})}
                className={`w-full p-2 border rounded-md ${
                  formValidation.company_name 
                    ? 'border-red-500' 
                    : isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300'
                }`}
                placeholder="Enter your company name"
              />
              {formValidation.company_name && (
                <p className="text-red-500 text-sm mt-1">{formValidation.company_name}</p>
              )}
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Company Type
              </label>
              <input
                type="text"
                value={policyForm.company_type || ''}
                onChange={(e) => setPolicyForm({...policyForm, company_type: e.target.value})}
                className={`w-full p-2 border rounded-md ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300'}`}
                placeholder="e.g., Technology Company, SaaS Provider"
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Industry *
              </label>
              <select
                value={policyForm.industry || ''}
                onChange={(e) => setPolicyForm({...policyForm, industry: e.target.value})}
                className={`w-full p-2 border rounded-md ${
                  formValidation.industry 
                    ? 'border-red-500' 
                    : isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300'
                }`}
              >
                <option value="">Select Industry</option>
                {industries.map(industry => (
                  <option key={industry} value={industry}>{industry}</option>
                ))}
              </select>
              {formValidation.industry && (
                <p className="text-red-500 text-sm mt-1">{formValidation.industry}</p>
              )}
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Employee Count *
              </label>
              <input
                type="number"
                value={policyForm.employee_count || ''}
                onChange={(e) => setPolicyForm({...policyForm, employee_count: parseInt(e.target.value)})}
                className={`w-full p-2 border rounded-md ${
                  formValidation.employee_count 
                    ? 'border-red-500' 
                    : isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300'
                }`}
                placeholder="Number of employees"
                min="1"
              />
              {formValidation.employee_count && (
                <p className="text-red-500 text-sm mt-1">{formValidation.employee_count}</p>
              )}
            </div>
          </div>
        </div>

        {/* Framework Selection */}
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <h3 className={`text-lg font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Compliance Framework *
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {frameworks.map(framework => (
              <label key={framework.value} className={`border rounded-lg p-4 cursor-pointer transition-all ${
                policyForm.framework === framework.value
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : isDarkMode ? 'border-gray-600 hover:border-gray-500' : 'border-gray-300 hover:border-gray-400'
              }`}>
                <input
                  type="radio"
                  name="framework"
                  value={framework.value}
                  checked={policyForm.framework === framework.value}
                  onChange={(e) => setPolicyForm({...policyForm, framework: e.target.value})}
                  className="sr-only"
                />
                <div className={`font-medium mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {framework.label}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {framework.description}
                </div>
              </label>
            ))}
          </div>
          {formValidation.framework && (
            <p className="text-red-500 text-sm mt-2">{formValidation.framework}</p>
          )}
        </div>

        {/* Data Types */}
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <h3 className={`text-lg font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Data Types Processed *
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {dataTypes.map(dataType => (
              <label key={dataType} className="flex items-center">
                <input
                  type="checkbox"
                  checked={(policyForm.data_types || []).includes(dataType)}
                  onChange={(e) => {
                    const currentTypes = policyForm.data_types || [];
                    if (e.target.checked) {
                      setPolicyForm({...policyForm, data_types: [...currentTypes, dataType]});
                    } else {
                      setPolicyForm({...policyForm, data_types: currentTypes.filter(type => type !== dataType)});
                    }
                  }}
                  className="mr-2"
                />
                <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {dataType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              </label>
            ))}
          </div>
          {formValidation.data_types && (
            <p className="text-red-500 text-sm mt-2">{formValidation.data_types}</p>
          )}
        </div>

        {/* Risk Profile (Enhanced Mode Only) */}
        {enhancedMode && (
          <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <h3 className={`text-lg font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Risk Profile
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {riskProfiles.map(risk => (
                <label key={risk.value} className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  policyForm.risk_profile === risk.value
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : isDarkMode ? 'border-gray-600 hover:border-gray-500' : 'border-gray-300 hover:border-gray-400'
                }`}>
                  <input
                    type="radio"
                    name="risk_profile"
                    value={risk.value}
                    checked={policyForm.risk_profile === risk.value}
                    onChange={(e) => setPolicyForm({...policyForm, risk_profile: e.target.value})}
                    className="sr-only"
                  />
                  <div className={`font-medium mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {risk.label}
                  </div>
                  <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    {risk.description}
                  </div>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Generate Button */}
        <div className="flex justify-center">
          <button
            onClick={enhancedMode ? handleEnhancedGeneration : () => generatePolicy()}
            disabled={loading || generationStage}
            className={`px-8 py-3 rounded-lg font-semibold text-white transition-all ${
              loading || generationStage
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
            }`}
          >
            {loading || generationStage ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Generating {enhancedMode ? 'Enhanced' : 'Legacy'} Policy...
              </div>
            ) : (
              `Generate ${enhancedMode ? 'Enhanced' : 'Legacy'} Policy`
            )}
          </button>
        </div>

        {/* Expected Output Summary (Enhanced Mode) */}
        {enhancedMode && (
          <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Expected Enhanced Policy Output:
            </h4>
            <ul className={`text-sm space-y-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              <li>• 20+ pages of comprehensive policy documentation</li>
              <li>• Detailed legal framework with regulatory mapping</li>
              <li>• Step-by-step implementation procedures</li>
              <li>• Ready-to-use forms, checklists, and templates</li>
              <li>• Decision trees and risk assessment matrices</li>
              <li>• Professional formatting suitable for audits</li>
              <li>• Quality score >90% for enterprise compliance</li>
            </ul>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <MultiStageProgressIndicator />
      
      {/* Company Profile Form */}
      <EnhancedCompanyProfileForm />
      
      {/* Error Display */}
      {error && (
        <div className={`p-4 rounded-lg border border-red-500 ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'}`}>
          <p className="text-red-600 font-medium">Generation Failed</p>
          <p className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-700'}`}>{error}</p>
        </div>
      )}
      
      {/* Generated Policy Preview */}
      {generatedPolicy && (
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <div className="flex justify-between items-start mb-4">
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Generated Policy: {generatedPolicy.title}
            </h3>
            {generatedPolicy.metadata && (
              <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                <div>Pages: {generatedPolicy.metadata.total_pages || 'N/A'}</div>
                <div>Words: {generatedPolicy.metadata.total_words?.toLocaleString() || 'N/A'}</div>
                {generatedPolicy.metadata.overall_completeness_score && (
                  <div>Quality: {(generatedPolicy.metadata.overall_completeness_score * 100).toFixed(1)}%</div>
                )}
              </div>
            )}
          </div>
          
          <div className={`p-3 rounded border max-h-96 overflow-y-auto ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
            <pre className={`whitespace-pre-wrap text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              {generatedPolicy.content}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedPolicyGenerator;