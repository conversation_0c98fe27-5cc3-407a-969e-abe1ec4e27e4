import React from 'react';

const Dashboard = ({ 
  dashboardData, 
  frameworks, 
  policies, 
  projects, 
  getFrameworkColor, 
  getStatusColor, 
  formatProgress 
}) => {
  return (
    <div className="space-y-8">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Policies</p>
              <p className="text-2xl font-semibold text-gray-900">
                {dashboardData?.stats?.total_policies || 0}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Projects</p>
              <p className="text-2xl font-semibold text-gray-900">
                {dashboardData?.stats?.total_projects || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Progress</p>
              <p className="text-2xl font-semibold text-gray-900">
                {dashboardData?.stats?.average_progress || 0}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Frameworks</p>
              <p className="text-2xl font-semibold text-gray-900">
                {dashboardData?.stats?.frameworks_supported || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Integrations</p>
              <p className="text-2xl font-semibold text-gray-900">
                {dashboardData?.stats?.total_integrations || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Evidence</p>
              <p className="text-2xl font-semibold text-gray-900">
                {dashboardData?.stats?.evidence_collected || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Framework Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {(frameworks || []).map((framework) => {
            const progress = Math.floor(Math.random() * 100); // Simulated progress
            const status = progress >= 80 ? 'compliant' : progress >= 50 ? 'partial' : 'non-compliant';
            const statusColor = status === 'compliant' ? 'green' : status === 'partial' ? 'yellow' : 'red';
            
            return (
              <div key={framework.id} className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {framework.name}
                  </h3>
                  {/* Traffic Light Indicator */}
                  <div className="flex items-center space-x-1">
                    <div className={`w-3 h-3 rounded-full ${statusColor === 'red' ? 'bg-red-500' : 'bg-gray-300'}`}></div>
                    <div className={`w-3 h-3 rounded-full ${statusColor === 'yellow' ? 'bg-yellow-500' : 'bg-gray-300'}`}></div>
                    <div className={`w-3 h-3 rounded-full ${statusColor === 'green' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Progress</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        statusColor === 'green' ? 'bg-green-500' : 
                        statusColor === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
                
                {/* Controls Status */}
                <div className="space-y-2">
                  <div className="text-xs text-gray-500 uppercase font-semibold">Controls Status</div>
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                      <span className="text-gray-600">{Math.floor(Math.random() * 10) + 5} Complete</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></div>
                      <span className="text-gray-600">{Math.floor(Math.random() * 5) + 1} Partial</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                      <span className="text-gray-600">{Math.floor(Math.random() * 3)} Missing</span>
                    </div>
                  </div>
                </div>
                
                {/* Framework Details */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Status:</span>
                    <span className={`font-medium capitalize ${
                      statusColor === 'green' ? 'text-green-600' : 
                      statusColor === 'yellow' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {status.replace('-', ' ')}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm mt-1">
                    <span className="text-gray-500">Last Updated:</span>
                    <span className="text-gray-600">
                      {new Date().toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

      {/* Framework Cards */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Supported Frameworks</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {(frameworks || []).map((framework) => (
              <div key={framework.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <div className={`w-3 h-3 rounded-full ${getFrameworkColor ? getFrameworkColor(framework.id) : 'bg-blue-500'} mr-3`}></div>
                  <h3 className="text-lg font-semibold text-gray-900">{framework.name}</h3>
                </div>
                <p className="text-sm text-gray-600 mb-3">{framework.description}</p>
                <div className="text-xs text-gray-500">
                  {framework.controls?.length || 0} controls
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Policies */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Recent Policies</h2>
          </div>
          <div className="p-6">
            {(policies || []).length > 0 ? (
              <div className="space-y-4">
                {(policies || []).map((policy) => (
                  <div key={policy.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{policy.title}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(policy.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor ? getStatusColor(policy.status) : 'bg-gray-100 text-gray-800'}`}>
                      {policy.status}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No policies yet. Generate your first policy!</p>
            )}
          </div>
        </div>

        {/* Recent Projects */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Recent Projects</h2>
          </div>
          <div className="p-6">
            {(projects || []).length > 0 ? (
              <div className="space-y-4">
                {(projects || []).map((project) => (
                  <div key={project.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900">
                        {project.company_name} - {project.framework?.toUpperCase()}
                      </p>
                      <span className="text-xs text-gray-500">
                        {formatProgress ? formatProgress(project.progress) : project.progress || 0}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getFrameworkColor ? getFrameworkColor(project.framework) : 'bg-blue-500'}`}
                        style={{ width: `${formatProgress ? formatProgress(project.progress) : project.progress || 0}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No projects yet. Create your first project!</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;