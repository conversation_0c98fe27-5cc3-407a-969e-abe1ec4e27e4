import React, { useState, useEffect } from 'react';

const AuditorPortal = ({ 
  backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001'
}) => {
  const [auditRequests, setAuditRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedAudit, setSelectedAudit] = useState(null);
  const [auditorAccess, setAuditorAccess] = useState({
    company_name: '',
    auditor_email: '',
    access_duration: 7, // days
    framework: 'gdpr'
  });

  const SecurityIcon = () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  );

  const KeyIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
    </svg>
  );

  const LinkIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
    </svg>
  );

  const generateAuditorAccess = async () => {
    if (!auditorAccess.company_name || !auditorAccess.auditor_email) {
      alert('Please fill in all required fields');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`${backendUrl}/api/auditor/create-access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(auditorAccess)
      });

      if (response.ok) {
        const result = await response.json();
        alert('Auditor access created successfully!');
        setAuditorAccess({
          company_name: '',
          auditor_email: '',
          access_duration: 7,
          framework: 'gdpr'
        });
        fetchAuditRequests(); // Refresh the list
      } else {
        throw new Error('Failed to create auditor access');
      }
    } catch (error) {
      console.error('Error creating auditor access:', error);
      alert('Failed to create auditor access. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchAuditRequests = async () => {
    try {
      const response = await fetch(`${backendUrl}/api/auditor/audit-requests`);
      if (response.ok) {
        const data = await response.json();
        setAuditRequests(data.audit_requests || []);
      }
    } catch (error) {
      console.error('Error fetching audit requests:', error);
    }
  };

  const generateAuditPackage = async (companyName, framework) => {
    setLoading(true);
    try {
      const response = await fetch(`${backendUrl}/api/audit/generate-package`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          company_name: companyName,
          framework: framework
        })
      });

      if (response.ok) {
        const result = await response.json();
        alert('Audit package generated successfully!');
        return result;
      } else {
        throw new Error('Failed to generate audit package');
      }
    } catch (error) {
      console.error('Error generating audit package:', error);
      alert('Failed to generate audit package. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAuditRequests();
  }, []);

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <SecurityIcon />
          <h1 className="text-3xl font-bold text-gray-900 ml-3">Auditor Portal</h1>
        </div>
        <p className="text-gray-600">
          Secure access management for external auditors and compliance reviews
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Create Auditor Access */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <KeyIcon />
            <span className="ml-2">Create Auditor Access</span>
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Name
              </label>
              <input
                type="text"
                value={auditorAccess.company_name}
                onChange={(e) => setAuditorAccess({
                  ...auditorAccess,
                  company_name: e.target.value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter company name for audit"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Auditor Email
              </label>
              <input
                type="email"
                value={auditorAccess.auditor_email}
                onChange={(e) => setAuditorAccess({
                  ...auditorAccess,
                  auditor_email: e.target.value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Compliance Framework
              </label>
              <select
                value={auditorAccess.framework}
                onChange={(e) => setAuditorAccess({
                  ...auditorAccess,
                  framework: e.target.value
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="gdpr">GDPR</option>
                <option value="soc2">SOC 2</option>
                <option value="iso27001">ISO 27001</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Access Duration (Days)
              </label>
              <select
                value={auditorAccess.access_duration}
                onChange={(e) => setAuditorAccess({
                  ...auditorAccess,
                  access_duration: parseInt(e.target.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={3}>3 Days</option>
                <option value={7}>7 Days</option>
                <option value={14}>14 Days</option>
                <option value={30}>30 Days</option>
              </select>
            </div>

            <button
              onClick={generateAuditorAccess}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center justify-center"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              ) : (
                <KeyIcon />
              )}
              <span className="ml-2">Generate Secure Access</span>
            </button>
          </div>
        </div>

        {/* Active Audit Requests */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <LinkIcon />
            <span className="ml-2">Active Audit Requests</span>
          </h2>

          {auditRequests.length === 0 ? (
            <div className="text-center py-8">
              <SecurityIcon />
              <p className="text-gray-500 mt-2">No active audit requests</p>
              <p className="text-sm text-gray-400">Create auditor access to see requests here</p>
            </div>
          ) : (
            <div className="space-y-4">
              {auditRequests.map((request, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold text-gray-900">{request.company_name}</h3>
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      {request.framework?.toUpperCase()}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    Auditor: {request.auditor_email}
                  </p>
                  
                  <p className="text-sm text-gray-500 mb-3">
                    Expires: {new Date(request.expires_at).toLocaleDateString()}
                  </p>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => generateAuditPackage(request.company_name, request.framework)}
                      className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                    >
                      Generate Package
                    </button>
                    <button
                      onClick={() => setSelectedAudit(request)}
                      className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                    >
                      View Details
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Security Features */}
      <div className="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">Security Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-start">
            <div className="bg-blue-100 rounded-full p-2 mr-3">
              <SecurityIcon />
            </div>
            <div>
              <h4 className="font-semibold text-blue-900">Time-Limited Access</h4>
              <p className="text-blue-700 text-sm">Auditor access automatically expires after the specified duration</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="bg-blue-100 rounded-full p-2 mr-3">
              <KeyIcon />
            </div>
            <div>
              <h4 className="font-semibold text-blue-900">Secure Tokens</h4>
              <p className="text-blue-700 text-sm">Unique access tokens for each auditor with encryption</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="bg-blue-100 rounded-full p-2 mr-3">
              <LinkIcon />
            </div>
            <div>
              <h4 className="font-semibold text-blue-900">Audit Trail</h4>
              <p className="text-blue-700 text-sm">Complete logging of all auditor activities and access</p>
            </div>
          </div>
        </div>
      </div>

      {/* Audit Request Details Modal */}
      {selectedAudit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Audit Request Details</h3>
            
            <div className="space-y-3">
              <div>
                <span className="font-medium">Company:</span> {selectedAudit.company_name}
              </div>
              <div>
                <span className="font-medium">Framework:</span> {selectedAudit.framework?.toUpperCase()}
              </div>
              <div>
                <span className="font-medium">Auditor:</span> {selectedAudit.auditor_email}
              </div>
              <div>
                <span className="font-medium">Created:</span> {new Date(selectedAudit.created_at).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium">Expires:</span> {new Date(selectedAudit.expires_at).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium">Status:</span> 
                <span className="ml-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  Active
                </span>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setSelectedAudit(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Close
              </button>
              <button
                onClick={() => {
                  generateAuditPackage(selectedAudit.company_name, selectedAudit.framework);
                  setSelectedAudit(null);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Generate Package
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuditorPortal;