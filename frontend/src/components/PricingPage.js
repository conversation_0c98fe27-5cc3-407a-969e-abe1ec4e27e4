import React, { useState } from 'react';

const PricingPage = ({ isDarkMode, user, onUpgrade }) => {
  const [billingCycle, setBillingCycle] = useState('monthly'); // 'monthly' or 'annual'
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [loading, setLoading] = useState(false);

  const plans = [
    {
      id: 'starter',
      name: 'Starter',
      description: 'Perfect for small teams getting started with compliance',
      monthlyPrice: 99,
      annualPrice: 990, // 2 months free
      features: [
        '1 compliance framework (GDPR, SOC2, or ISO27001)',
        'Up to 5 AI-generated policies',
        '3 business tool integrations',
        'Basic compliance dashboard',
        'Email support',
        'Up to 25 employees'
      ],
      popular: false,
      cta: 'Start Free Trial'
    },
    {
      id: 'professional',
      name: 'Professional',
      description: 'Most popular for growing UK SMBs',
      monthlyPrice: 249,
      annualPrice: 2490, // 2 months free
      features: [
        'All 3 compliance frameworks (GDPR + SOC2 + ISO27001)',
        'Unlimited AI-generated policies',
        '15+ business tool integrations',
        'Advanced compliance dashboard with traffic lights',
        'Automated evidence collection',
        'PDF/Word policy exports with branding',
        'Priority email & chat support',
        'Up to 100 employees'
      ],
      popular: true,
      cta: 'Most Popular'
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'For larger teams with advanced compliance needs',
      monthlyPrice: 499,
      annualPrice: 4990, // 2 months free
      features: [
        'Everything in Professional',
        '25+ business tool integrations',
        'Automated screenshot evidence capture',
        'Secure auditor portal with time-limited access',
        'Advanced version control & audit trails',
        'White-label compliance reporting',
        'Dedicated customer success manager',
        'Phone support + SLA',
        'Up to 200 employees'
      ],
      popular: false,
      cta: 'Contact Sales'
    }
  ];

  const getPrice = (plan) => {
    return billingCycle === 'monthly' ? plan.monthlyPrice : Math.floor(plan.annualPrice / 12);
  };

  const getSavings = (plan) => {
    const monthlyTotal = plan.monthlyPrice * 12;
    const annualPrice = plan.annualPrice;
    return monthlyTotal - annualPrice;
  };

  const handleSelectPlan = async (plan) => {
    if (plan.id === 'enterprise') {
      // Handle enterprise contact sales
      window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank');
      return;
    }

    setSelectedPlan(plan);
    setLoading(true);

    try {
      // Simulate Stripe checkout process
      const checkoutData = {
        planId: plan.id,
        billingCycle: billingCycle,
        priceId: `price_${plan.id}_${billingCycle}`,
        amount: billingCycle === 'monthly' ? plan.monthlyPrice : plan.annualPrice,
        currency: 'gbp',
        user: user
      };

      // In production, this would call Stripe checkout
      console.log('Initiating Stripe checkout:', checkoutData);
      
      // Simulate checkout success after delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update user plan
      const updatedUser = {
        ...user,
        plan: plan.id,
        billingCycle: billingCycle,
        subscriptionStatus: 'active',
        subscriptionStart: new Date().toISOString()
      };
      
      localStorage.setItem('complianceGPT-user', JSON.stringify(updatedUser));
      
      if (onUpgrade) {
        onUpgrade(updatedUser);
      }
      
      alert(`Successfully upgraded to ${plan.name} plan!`);
      
    } catch (error) {
      console.error('Payment error:', error);
      alert('Payment failed. Please try again.');
    } finally {
      setLoading(false);
      setSelectedPlan(null);
    }
  };

  const CheckIcon = () => (
    <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
  );

  const currentPlan = user?.plan || 'trial';

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Simple, Transparent Pricing
            </span>
          </h1>
          <p className={`text-xl mb-8 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Choose the perfect plan for your compliance automation needs
          </p>
          
          {/* ROI Calculator */}
          <div className={`inline-flex p-1 rounded-lg border ${
            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
          }`}>
            <div className={`px-4 py-2 rounded-md text-sm font-medium ${
              isDarkMode ? 'text-green-400' : 'text-green-600'
            }`}>
              💰 Save £50,000+ vs hiring compliance consultants
            </div>
          </div>
        </div>

        {/* Billing Toggle */}
        <div className="flex justify-center mb-12">
          <div className={`inline-flex p-1 rounded-lg border ${
            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
          }`}>
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingCycle === 'monthly'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode
                    ? 'text-gray-300 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('annual')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors relative ${
                billingCycle === 'annual'
                  ? 'bg-blue-600 text-white'
                  : isDarkMode
                    ? 'text-gray-300 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Annual
              <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1 rounded">
                Save 17%
              </span>
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative rounded-xl border transition-all duration-300 hover:scale-105 ${
                plan.popular
                  ? 'border-blue-500 shadow-xl'
                  : isDarkMode
                    ? 'border-gray-700 hover:border-gray-600'
                    : 'border-gray-200 hover:border-gray-300'
              } ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg z-10">
                    Most Popular
                  </div>
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-6">
                  <h3 className={`text-2xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {plan.name}
                  </h3>
                  <p className={`text-sm mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {plan.description}
                  </p>
                  
                  {/* Current Plan Badge */}
                  {currentPlan === plan.id && (
                    <div className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mb-4">
                      ✅ Current Plan
                    </div>
                  )}

                  {/* Price */}
                  <div className="mb-4">
                    <span className={`text-4xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      £{getPrice(plan)}
                    </span>
                    <span className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      /month
                    </span>
                    {billingCycle === 'annual' && (
                      <div className="text-sm text-green-600 mt-1">
                        Save £{getSavings(plan)}/year
                      </div>
                    )}
                  </div>
                </div>

                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckIcon />
                      <span className={`ml-3 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <button
                  onClick={() => handleSelectPlan(plan)}
                  disabled={loading || currentPlan === plan.id}
                  className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 ${
                    currentPlan === plan.id
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : plan.popular
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                        : isDarkMode
                          ? 'bg-gray-700 text-white hover:bg-gray-600'
                          : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                  } ${loading && selectedPlan?.id === plan.id ? 'opacity-50' : ''}`}
                >
                  {loading && selectedPlan?.id === plan.id ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Processing...
                    </div>
                  ) : currentPlan === plan.id ? (
                    'Current Plan'
                  ) : (
                    plan.cta
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Features Comparison */}
        <div className={`rounded-xl p-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} border ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <h3 className={`text-2xl font-bold text-center mb-8 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Why ComplianceGPT?
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl mb-4">⚡</div>
              <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                95% Faster
              </h4>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Generate policies in minutes, not weeks
              </p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-4">💰</div>
              <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                £50K+ Savings
              </h4>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Replace expensive compliance consultants
              </p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-4">🤖</div>
              <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                AI-Powered
              </h4>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Latest Gemini 2.5 Flash technology
              </p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-4">🇬🇧</div>
              <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                UK-Focused
              </h4>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Built specifically for UK SMBs
              </p>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <h3 className={`text-2xl font-bold text-center mb-8 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Frequently Asked Questions
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} border ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                What's included in the free trial?
              </h4>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                14-day free trial with full access to generate 5 policies, connect 3 integrations, and explore all features.
              </p>
            </div>
            
            <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} border ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Can I change plans anytime?
              </h4>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Yes! Upgrade or downgrade your plan anytime. Changes take effect on your next billing cycle.
              </p>
            </div>
            
            <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} border ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Is my data secure?
              </h4>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Yes! We use enterprise-grade encryption, SOC 2 compliance, and UK data residency to keep your data safe.
              </p>
            </div>
            
            <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} border ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Do you offer refunds?
              </h4>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Yes! 30-day money-back guarantee. If you're not satisfied, we'll refund your payment in full.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Sales CTA */}
        <div className={`mt-16 text-center p-8 rounded-xl ${
          isDarkMode ? 'bg-gradient-to-r from-gray-800 to-gray-700' : 'bg-gradient-to-r from-blue-50 to-purple-50'
        }`}>
          <h3 className={`text-2xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Need a custom solution?
          </h3>
          <p className={`text-lg mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Contact our sales team for enterprise pricing, custom integrations, or volume discounts.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-semibold"
            >
              Contact Sales
            </a>
            <a
              href="https://calendly.com/compliancegpt/demo"
              target="_blank"
              rel="noopener noreferrer"
              className={`px-8 py-3 rounded-lg font-semibold border-2 transition-all duration-300 ${
                isDarkMode
                  ? 'border-gray-600 text-gray-300 hover:border-gray-500 hover:bg-gray-800'
                  : 'border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50'
              }`}
            >
              Schedule Demo
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPage;