
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Policies = () => {
  const [policies, setPolicies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPolicy, setSelectedPolicy] = useState(null);
  const [versions, setVersions] = useState([]);
  const [showVersions, setShowVersions] = useState(false);

  const apiUrl = process.env.REACT_APP_BACKEND_URL || 'https://5fb85ea3-d551-4232-b367-a6d8d4edbcbe.preview.emergentagent.com';

  useEffect(() => {
    const fetchPolicies = async () => {
      try {
        const response = await axios.get(`${apiUrl}/api/policies`);
        setPolicies(response.data.policies || []);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching policies:', err);
        setError('Failed to load policies. Please try again later.');
        setLoading(false);
      }
    };

    fetchPolicies();
  }, [apiUrl]);

  const handleExportPDF = async (policyId) => {
    try {
      window.open(`${apiUrl}/api/policies/${policyId}/export/pdf`, '_blank');
    } catch (err) {
      console.error('Error exporting PDF:', err);
      setError('Failed to export PDF. Please try again later.');
    }
  };

  const handleExportWord = async (policyId) => {
    try {
      window.open(`${apiUrl}/api/policies/${policyId}/export/word`, '_blank');
    } catch (err) {
      console.error('Error exporting Word document:', err);
      setError('Failed to export Word document. Please try again later.');
    }
  };

  const handleViewVersions = async (policyId) => {
    try {
      const response = await axios.get(`${apiUrl}/api/policies/${policyId}/versions`);
      setVersions(response.data.versions || []);
      setSelectedPolicy(policyId);
      setShowVersions(true);
    } catch (err) {
      console.error('Error fetching versions:', err);
      setError('Failed to load policy versions. Please try again later.');
    }
  };

  const handleCreateVersion = async (policyId) => {
    try {
      const response = await axios.post(`${apiUrl}/api/policies/${policyId}/versions`, {
        content: "Updated policy content",
        title: "Updated Policy",
        changes_summary: "Minor updates to comply with new regulations",
        user_id: "current_user"
      });
      
      // Refresh versions list
      handleViewVersions(policyId);
    } catch (err) {
      console.error('Error creating version:', err);
      setError('Failed to create new version. Please try again later.');
    }
  };

  const handleApproveVersion = async (policyId, version) => {
    try {
      await axios.post(`${apiUrl}/api/policies/${policyId}/versions/${version}/approve`, {
        approver_id: "current_user",
        approval_notes: "Approved after review"
      });
      
      // Refresh versions list
      handleViewVersions(policyId);
    } catch (err) {
      console.error('Error approving version:', err);
      setError('Failed to approve version. Please try again later.');
    }
  };

  const handleCompareVersions = async (policyId) => {
    try {
      if (versions.length < 2) {
        setError('Need at least 2 versions to compare');
        return;
      }
      
      const version1 = versions[0].version;
      const version2 = versions[versions.length - 1].version;
      
      const response = await axios.get(
        `${apiUrl}/api/policies/${policyId}/versions/compare?version1=${version1}&version2=${version2}`
      );
      
      // In a real app, you would display the differences here
      console.log('Version comparison:', response.data);
    } catch (err) {
      console.error('Error comparing versions:', err);
      setError('Failed to compare versions. Please try again later.');
    }
  };

  const handleRollback = async (policyId, targetVersion) => {
    try {
      await axios.post(`${apiUrl}/api/policies/${policyId}/rollback`, {
        target_version: targetVersion,
        user_id: "current_user",
        rollback_reason: "Rolling back to stable version"
      });
      
      // Refresh policies list
      const response = await axios.get(`${apiUrl}/api/policies`);
      setPolicies(response.data.policies || []);
      
      // Close versions panel
      setShowVersions(false);
    } catch (err) {
      console.error('Error rolling back policy:', err);
      setError('Failed to rollback policy. Please try again later.');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Policies</h1>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Policies</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Policies</h1>
      
      {showVersions ? (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Policy Versions</h2>
            <button
              onClick={() => setShowVersions(false)}
              className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
            >
              Back to Policies
            </button>
          </div>
          
          <div className="mb-4 flex space-x-4">
            <button
              onClick={() => handleCreateVersion(selectedPolicy)}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Create New Version
            </button>
            
            <button
              onClick={() => handleCompareVersions(selectedPolicy)}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
              disabled={versions.length < 2}
            >
              Compare Versions
            </button>
          </div>
          
          <div className="bg-white shadow overflow-hidden rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Version</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {versions.map((version) => (
                  <tr key={version.version}>
                    <td className="px-6 py-4 whitespace-nowrap">{version.version}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{version.created_by}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        version.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {version.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">{new Date(version.created_at).toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {version.status !== 'approved' && (
                        <button
                          onClick={() => handleApproveVersion(selectedPolicy, version.version)}
                          className="text-indigo-600 hover:text-indigo-900 mr-4"
                        >
                          Approve
                        </button>
                      )}
                      <button
                        onClick={() => handleRollback(selectedPolicy, version.version)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Rollback to this version
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Framework</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {policies.length > 0 ? (
                policies.map((policy) => (
                  <tr key={policy.id}>
                    <td className="px-6 py-4 whitespace-nowrap">{policy.title}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{policy.framework}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{new Date(policy.created_at).toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleExportPDF(policy.id)}
                        className="text-blue-600 hover:text-blue-900 mr-4"
                      >
                        Export as PDF
                      </button>
                      <button
                        onClick={() => handleExportWord(policy.id)}
                        className="text-green-600 hover:text-green-900 mr-4"
                      >
                        Export as Word
                      </button>
                      <button
                        onClick={() => handleViewVersions(policy.id)}
                        className="text-purple-600 hover:text-purple-900"
                      >
                        Versions
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="4" className="px-6 py-4 text-center text-gray-500">
                    No policies found. Generate a policy using the AI Policy Generator.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default Policies;
