import React, { useState, useEffect } from 'react';
import { API_BASE } from '../utils/constants';

const AIAssistant = () => {
  const [activeFeature, setActiveFeature] = useState('risk-assessment');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [companyProfile, setCompanyProfile] = useState({
    name: 'TechCorp Ltd',
    industry: 'Software Development',
    employee_count: 25,
    revenue: '£2M',
    data_types: ['Personal Data', 'Financial Data'],
    target_markets: ['UK', 'EU']
  });

  // Icons
  const AIIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    </svg>
  );

  const ShieldIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  );

  const DocumentIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  );

  const ChartIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );

  const features = [
    {
      id: 'risk-assessment',
      name: 'AI Risk Assessment',
      description: 'Comprehensive compliance risk analysis using Gemini 2.5 Flash',
      icon: <ShieldIcon />,
      color: 'bg-red-500'
    },
    {
      id: 'framework-recommendation',
      name: 'Framework Recommendation',
      description: 'AI-powered recommendations for optimal compliance frameworks',
      icon: <AIIcon />,
      color: 'bg-blue-500'
    },
    {
      id: 'audit-readiness',
      name: 'Audit Readiness Analysis',
      description: 'Evaluate audit preparedness and get actionable recommendations',
      icon: <ChartIcon />,
      color: 'bg-green-500'
    },
    {
      id: 'compliance-report',
      name: 'Professional Reports',
      description: 'Generate audit-ready PDF reports and documentation packages',
      icon: <DocumentIcon />,
      color: 'bg-purple-500'
    }
  ];

  const generateRiskAssessment = async () => {
    setLoading(true);
    try {
      // Mock AI risk assessment - in a real implementation this would call the API
      const mockAssessment = {
        overall_risk_score: 65,
        assessment_summary: "Medium-High Risk Profile for Software Development Company",
        framework_risks: {
          gdpr: { risk_score: 70, priority: "High", key_issues: ["Data processing consent", "Cross-border transfers", "Data retention policies"] },
          soc2: { risk_score: 60, priority: "Medium", key_issues: ["Access controls", "System monitoring", "Incident response"] },
          iso27001: { risk_score: 55, priority: "Medium", key_issues: ["Asset management", "Risk assessment", "Supplier relationships"] }
        },
        industry_risks: [
          "Software vulnerabilities in customer systems",
          "API security and data exposure risks", 
          "Cloud infrastructure compliance gaps",
          "Remote work security challenges"
        ],
        immediate_actions: [
          "Implement comprehensive data mapping for GDPR compliance",
          "Establish formal incident response procedures",
          "Deploy automated security monitoring tools",
          "Conduct employee privacy and security training",
          "Review and update vendor management processes"
        ],
        cost_benefit: {
          implementation_cost: "£15,000 - £25,000",
          non_compliance_risk: "£50,000 - £200,000 in potential fines",
          roi_timeline: "6-12 months"
        },
        generated_by: "Gemini 2.5 Flash AI",
        confidence_level: "High"
      };

      setResults(mockAssessment);
    } catch (error) {
      console.error('Risk assessment failed:', error);
      setResults({ error: 'Risk assessment simulation failed' });
    } finally {
      setLoading(false);
    }
  };

  const generateFrameworkRecommendation = async () => {
    setLoading(true);
    try {
      const mockRecommendation = {
        primary_recommendation: {
          framework: "GDPR",
          priority: 1,
          justification: "As a UK software company processing personal data, GDPR compliance is legally mandatory and provides foundation for other frameworks.",
          business_impact: "Enables EU market expansion, builds customer trust, reduces legal risk",
          implementation_timeline: "3-4 months",
          estimated_cost: "£8,000 - £15,000"
        },
        secondary_recommendations: [
          {
            framework: "SOC 2 Type II",
            priority: 2,
            justification: "Critical for enterprise B2B sales and demonstrates security maturity to potential customers.",
            business_impact: "Unlocks enterprise deals, improves sales cycle efficiency",
            implementation_timeline: "6-8 months",
            estimated_cost: "£12,000 - £20,000"
          },
          {
            framework: "ISO 27001",
            priority: 3,
            justification: "Provides comprehensive security management framework and international recognition.",
            business_impact: "Global market credibility, operational excellence, competitive advantage",
            implementation_timeline: "8-12 months", 
            estimated_cost: "£15,000 - £30,000"
          }
        ],
        implementation_roadmap: {
          "Months 1-4": "GDPR implementation - Data mapping, policies, consent mechanisms",
          "Months 5-8": "SOC 2 preparation - Security controls, monitoring, documentation",
          "Months 9-12": "ISO 27001 certification - ISMS implementation, gap analysis, audit"
        },
        roi_analysis: {
          potential_revenue_impact: "30-50% increase in enterprise deal closure rate",
          cost_avoidance: "£50,000+ in potential GDPR fines",
          competitive_advantage: "Positions as security-first vendor in market"
        }
      };

      setResults(mockRecommendation);
    } catch (error) {
      console.error('Framework recommendation failed:', error);
      setResults({ error: 'Framework recommendation simulation failed' });
    } finally {
      setLoading(false);
    }
  };

  const generateAuditReadiness = async () => {
    setLoading(true);
    try {
      const mockAuditReadiness = {
        overall_readiness_score: 78,
        framework: "GDPR",
        audit_probability: "85% likely to pass",
        strengths: [
          "Comprehensive privacy policies documented",
          "Automated evidence collection from business systems",
          "Strong technical security controls implemented",
          "Regular staff training program in place"
        ],
        critical_gaps: [
          "Data Processing Impact Assessments (DPIAs) not completed for high-risk processing",
          "Breach notification procedures need testing and documentation",
          "Third-party vendor agreements missing data protection clauses",
          "Data retention schedules not fully implemented"
        ],
        pre_audit_checklist: [
          "Complete outstanding DPIAs for marketing and analytics systems",
          "Conduct tabletop exercise for data breach response",
          "Update all vendor contracts with GDPR-compliant clauses",
          "Implement automated data retention and deletion processes",
          "Prepare evidence package demonstrating compliance controls"
        ],
        timeline_to_ready: "6-8 weeks with focused effort",
        estimated_audit_cost: "£8,000 - £12,000",
        certification_timeline: "3-4 months post-audit",
        auditor_recommendations: [
          "Engage auditor early for gap assessment",
          "Schedule regular check-ins during preparation",
          "Prepare detailed evidence documentation",
          "Train key staff for auditor interviews"
        ]
      };

      setResults(mockAuditReadiness);
    } catch (error) {
      console.error('Audit readiness analysis failed:', error);
      setResults({ error: 'Audit readiness simulation failed' });
    } finally {
      setLoading(false);
    }
  };

  const generateComplianceReport = async () => {
    setLoading(true);
    try {
      // Simulate PDF report generation
      const mockReport = {
        report_type: "Comprehensive Compliance Report",
        framework: "GDPR",
        pages: 45,
        sections: [
          "Executive Summary",
          "Company Profile & Scope",
          "Compliance Framework Overview", 
          "Implemented Policies (12 policies)",
          "Evidence Documentation (23 evidence items)",
          "Risk Assessment & Mitigation",
          "Gap Analysis & Recommendations",
          "Implementation Roadmap",
          "Audit Preparation Checklist"
        ],
        generated_at: new Date().toISOString(),
        file_size: "2.3 MB",
        download_ready: true,
        executive_summary: {
          compliance_status: "Substantially Compliant",
          overall_score: "78/100",
          key_achievements: [
            "12 comprehensive policies implemented",
            "23 evidence items automatically collected",
            "85% of GDPR controls covered",
            "Strong technical and administrative safeguards"
          ],
          action_items: 4,
          estimated_certification_timeline: "8-10 weeks"
        }
      };

      setResults(mockReport);
    } catch (error) {
      console.error('Report generation failed:', error);
      setResults({ error: 'Report generation simulation failed' });
    } finally {
      setLoading(false);
    }
  };

  const executeFeature = async () => {
    setResults(null);
    
    switch (activeFeature) {
      case 'risk-assessment':
        await generateRiskAssessment();
        break;
      case 'framework-recommendation':
        await generateFrameworkRecommendation();
        break;
      case 'audit-readiness':
        await generateAuditReadiness();
        break;
      case 'compliance-report':
        await generateComplianceReport();
        break;
      default:
        break;
    }
  };

  const renderResults = () => {
    if (!results) return null;

    if (results.error) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{results.error}</p>
        </div>
      );
    }

    switch (activeFeature) {
      case 'risk-assessment':
        return (
          <div className="space-y-6">
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">AI Risk Assessment Results</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-3xl font-bold text-red-600">{results.overall_risk_score}</div>
                  <div className="text-sm text-gray-600">Overall Risk Score</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-lg font-semibold text-blue-600">{results.confidence_level}</div>
                  <div className="text-sm text-gray-600">AI Confidence</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-lg font-semibold text-green-600">{results.cost_benefit?.roi_timeline}</div>
                  <div className="text-sm text-gray-600">ROI Timeline</div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Framework Risk Analysis</h4>
                  <div className="space-y-2">
                    {Object.entries(results.framework_risks || {}).map(([framework, risk]) => (
                      <div key={framework} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <span className="font-medium">{framework.toUpperCase()}</span>
                        <span className={`px-2 py-1 text-xs rounded ${
                          risk.priority === 'High' ? 'bg-red-100 text-red-800' :
                          risk.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          Risk: {risk.risk_score} - {risk.priority}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Immediate Actions Required</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                    {results.immediate_actions?.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      case 'framework-recommendation':
        return (
          <div className="space-y-6">
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Framework Recommendations</h3>
              
              <div className="mb-6 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                <h4 className="font-semibold text-blue-900">Primary Recommendation: {results.primary_recommendation?.framework}</h4>
                <p className="text-blue-800 mt-2">{results.primary_recommendation?.justification}</p>
                <div className="grid grid-cols-2 gap-4 mt-3 text-sm">
                  <div>
                    <span className="font-medium">Timeline:</span> {results.primary_recommendation?.implementation_timeline}
                  </div>
                  <div>
                    <span className="font-medium">Cost:</span> {results.primary_recommendation?.estimated_cost}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">Secondary Recommendations</h4>
                {results.secondary_recommendations?.map((rec, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium">{rec.framework}</h5>
                      <span className="text-sm text-gray-500">Priority {rec.priority}</span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{rec.justification}</p>
                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
                      <div>Timeline: {rec.implementation_timeline}</div>
                      <div>Cost: {rec.estimated_cost}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'audit-readiness':
        return (
          <div className="space-y-6">
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Audit Readiness Analysis</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-3xl font-bold text-green-600">{results.overall_readiness_score}%</div>
                  <div className="text-sm text-gray-600">Readiness Score</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-lg font-semibold text-blue-600">{results.audit_probability}</div>
                  <div className="text-sm text-gray-600">Pass Probability</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-lg font-semibold text-orange-600">{results.timeline_to_ready}</div>
                  <div className="text-sm text-gray-600">Time to Ready</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-green-900 mb-2">Strengths</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                    {results.strengths?.map((strength, index) => (
                      <li key={index} className="text-green-700">{strength}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-red-900 mb-2">Critical Gaps</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                    {results.critical_gaps?.map((gap, index) => (
                      <li key={index} className="text-red-700">{gap}</li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="font-semibold text-gray-900 mb-2">Pre-Audit Checklist</h4>
                <div className="space-y-2">
                  {results.pre_audit_checklist?.map((item, index) => (
                    <label key={index} className="flex items-start">
                      <input type="checkbox" className="mt-1 mr-3" />
                      <span className="text-sm text-gray-700">{item}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'compliance-report':
        return (
          <div className="space-y-6">
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Professional Compliance Report</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{results.pages}</div>
                  <div className="text-sm text-gray-600">Pages</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{results.sections?.length}</div>
                  <div className="text-sm text-gray-600">Sections</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-lg font-semibold text-purple-600">{results.file_size}</div>
                  <div className="text-sm text-gray-600">File Size</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-lg font-semibold text-orange-600">{results.executive_summary?.overall_score}</div>
                  <div className="text-sm text-gray-600">Compliance Score</div>
                </div>
              </div>

              <div className="mb-6 p-4 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-900 mb-2">Executive Summary</h4>
                <p className="text-green-800 mb-3">Status: {results.executive_summary?.compliance_status}</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Certification Timeline:</span> {results.executive_summary?.estimated_certification_timeline}
                  </div>
                  <div>
                    <span className="font-medium">Action Items:</span> {results.executive_summary?.action_items}
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Report Sections</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {results.sections?.map((section, index) => (
                    <div key={index} className="flex items-center p-2 bg-gray-50 rounded">
                      <DocumentIcon />
                      <span className="ml-2 text-sm text-gray-700">{section}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <button className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                  Download PDF Report
                </button>
                <p className="text-sm text-blue-700 mt-2">
                  Professional audit-ready documentation package ready for download
                </p>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      {/* AI Assistant Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
        <div className="flex items-center mb-4">
          <AIIcon />
          <h1 className="ml-3 text-2xl font-bold">ComplianceGPT AI Assistant</h1>
          <span className="ml-3 px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">
            Powered by Gemini 2.5 Flash
          </span>
        </div>
        <p className="text-blue-100">
          Advanced AI-powered compliance analysis and recommendations. Get expert guidance 
          for risk assessment, framework selection, audit preparation, and professional reporting.
        </p>
      </div>

      {/* Company Profile */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Company Profile</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={companyProfile.name}
              onChange={(e) => setCompanyProfile({...companyProfile, name: e.target.value})}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={companyProfile.industry}
              onChange={(e) => setCompanyProfile({...companyProfile, industry: e.target.value})}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Employee Count</label>
            <input
              type="number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={companyProfile.employee_count}
              onChange={(e) => setCompanyProfile({...companyProfile, employee_count: parseInt(e.target.value)})}
            />
          </div>
        </div>
      </div>

      {/* AI Feature Selection */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">AI-Powered Features</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {features.map((feature) => (
              <button
                key={feature.id}
                onClick={() => setActiveFeature(feature.id)}
                className={`p-4 rounded-lg border-2 transition-all text-left ${
                  activeFeature === feature.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center mb-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white ${feature.color}`}>
                    {feature.icon}
                  </div>
                  <h3 className="ml-3 font-medium text-gray-900">{feature.name}</h3>
                </div>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </button>
            ))}
          </div>

          <div className="flex justify-center">
            <button
              onClick={executeFeature}
              disabled={loading}
              className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Analyzing with AI...
                </>
              ) : (
                <>
                  <AIIcon />
                  <span className="ml-2">Run AI Analysis</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      {renderResults()}
    </div>
  );
};

export default AIAssistant;