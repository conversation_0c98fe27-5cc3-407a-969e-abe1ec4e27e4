{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"axios": "^1.8.4", "cra-template": "1.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.1", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "9.23.0", "autoprefixer": "^10.4.20", "eslint": "9.23.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.4", "globals": "15.15.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17"}, "overrides": {"ajv": "8.17.1", "ajv-keywords": "5.1.0", "schema-utils": "4.3.0"}}