# General
.DS_Store
Thumbs.db

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.egg-info/
*.egg
.env
.venv/
venv/
ENV/
env/
env.bak/
venv.bak/

# Coverage
.coverage
coverage.xml
htmlcov/
*.coverage
coverage_report.txt
coverage_report_module_path.txt

# Build & Distribution
build/
dist/
**/*.zip
**/*.tar.gz
**/*.tar
**/*.tgz
*.pack
*.deb

# IDEs & Editors
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Node / JS (from original)
node_modules/
/.pnp
.pnp.js
.yarn/install-state.gz
/.next/
/out/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.tsbuildinfo
next-env.d.ts

# Jupyter (from original)
.ipynb_checkpoints/

# Local tokens/credentials (from original)
*token.json*
*credentials.json*

# Cloud/Deployment Specific (from original)
.vercel
.ac # (Assuming this is cloud/tool specific)
agenthub/agents/youtube/db # (Specific path, from original)

# Testing & DB (from original)
/coverage # (Note: .coverage is more common for the file itself)
dump.rdb

# Chainlit (from original)
chainlit.md
.chainlit

# Misc (from original)
*.pem

# Tool specific (Windsurf, Cursor, Taskmaster etc.)
.cursor/
.memory-bank/
.roo/
.roomodes
.taskmasterconfig
.windsurf-ai-activation-prompt
.windsurf/
tasks.json
tasks/

# Logs (some already present)
logs/
*.log
dev-debug.log

# Specific generated reports
scripts/task-complexity-report.json

# Build caches
.cache/

# Android SDK
android-sdk/

# Project specific/example files to ignore
TASKMASTER_CHECKLIST.md
scripts/example_prd.txt

# Added by Claude Task Master
# Logs
logs
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
# OS specific
# Task files