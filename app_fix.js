// Fix for App.js

// The issue is that the PolicyGenerator component is not being rendered when activeTab === 'generate'
// Add the following code between lines 246-248 in App.js:

{/* Generate Tab */}
{activeTab === 'generate' && (
  <PolicyGenerator 
    policyForm={policyForm}
    setPolicyForm={setPolicyForm}
    frameworks={frameworks}
    generatePolicy={generatePolicy}
    generatingPolicy={generatingPolicy}
    generatedPolicy={generatedPolicy}
  />
)}

// This will render the PolicyGenerator component when the Generate tab is clicked
