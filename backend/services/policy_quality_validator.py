# /home/<USER>/Documents/Compliance-GPT/backend/services/policy_quality_validator.py
import re
from typing import List

class PolicyQualityValidator:
    """
    Validates the quality of generated policy content at various stages.
    Provides scores based on content analysis (e.g., keyword presence, structure).
    """

    def __init__(self):
        """Initializes the PolicyQualityValidator."""
        # Instance attributes for keyword lists (required by tests)
        self.data_collection_keywords = ["survey", "questionnaire", "interview", "assessment", "analysis", "collection", "gathering", "data", "information", "requirement"]
        self.risk_assessment_keywords = ["risk", "threat", "vulnerability", "impact", "likelihood", "assessment", "analysis", "evaluation", "mitigation", "control"]
        self.framework_keywords = ["article", "regulation", "data protection", "lawful basis", "rights", "gdpr", "controller", "processor"]
        self.procedures_keywords = ["step", "workflow", "process", "guideline", "responsibility", "procedure", "control"]
        self.tools_keywords = ["software", "platform", "encryption", "security", "solution", "tool", "technology"]
        self.assembly_keywords = ["table of contents", "introduction", "summary", "conclusion", "appendix", "version control"]

        # Keep class attributes for backward compatibility
        self.DATA_COLLECTION_KEYWORDS = self.data_collection_keywords
        self.RISK_ASSESSMENT_KEYWORDS = self.risk_assessment_keywords
        self.FRAMEWORK_KEYWORDS = self.framework_keywords
        self.PROCEDURES_KEYWORDS = self.procedures_keywords
        self.TOOLS_KEYWORDS = self.tools_keywords
        self.ASSEMBLY_KEYWORDS = self.assembly_keywords

    def _calculate_keyword_score(self, text: str, keywords: List[str]) -> float:
        """Calculates a score based on keyword presence and frequency."""
        if not text or not keywords:
            return 0.0
        
        text_lower = text.lower()
        found_keywords_count = 0
        
        for keyword in keywords:
            if keyword.lower() in text_lower:
                found_keywords_count += 1
        
        # Score primarily based on the variety of unique keywords found
        variety_score = found_keywords_count / len(keywords)
        
        # Simple length check bonus/penalty (very basic)
        word_count = len(text.split())
        if word_count < 100: # Penalize very short content
            variety_score *= 0.5
        elif word_count > 500: # Slightly reward longer content, up to a point
            variety_score = min(variety_score * 1.1, 1.0)
            
        return round(variety_score, 4)

    def validate_data_collection(self, data_collection_content: str) -> float:
        """Validates the data collection stage content."""
        score = self._calculate_keyword_score(data_collection_content, self.DATA_COLLECTION_KEYWORDS)
        
        # Additional structural checks for data collection
        content_lower = data_collection_content.lower()
        
        # Check for key data collection elements
        if "requirement" not in content_lower and "need" not in content_lower:
            score *= 0.9  # Penalize if no requirements analysis
            
        if "method" not in content_lower and "approach" not in content_lower:
            score *= 0.9  # Penalize if no methodology mentioned
            
        # Reward structured data collection approach
        if any(term in content_lower for term in ["survey", "interview", "questionnaire", "assessment"]):
            score = min(score * 1.1, 1.0)  # Slight bonus for structured approaches
            
        return round(score, 4)

    def validate_risk_assessment(self, risk_assessment_content: str) -> float:
        """Validates the risk assessment stage content."""
        score = self._calculate_keyword_score(risk_assessment_content, self.RISK_ASSESSMENT_KEYWORDS)
        
        # Additional structural checks for risk assessment
        content_lower = risk_assessment_content.lower()
        
        # Check for core risk assessment components
        if "risk" not in content_lower:
            score *= 0.8  # Heavy penalty if 'risk' is not mentioned
            
        if "impact" not in content_lower and "consequence" not in content_lower:
            score *= 0.9  # Penalize if no impact analysis
            
        if "likelihood" not in content_lower and "probability" not in content_lower:
            score *= 0.9  # Penalize if no likelihood assessment
            
        # Check for risk categories or types
        if any(term in content_lower for term in ["high", "medium", "low", "critical", "minor"]):
            score = min(score * 1.1, 1.0)  # Reward risk categorization
            
        # Check for mitigation mentions
        if any(term in content_lower for term in ["mitigation", "control", "treatment", "response"]):
            score = min(score * 1.05, 1.0)  # Small bonus for mitigation consideration
            
        return round(score, 4)

    async def validate_framework(self, framework_content: str) -> float:
        """Validates the generated legal framework content."""
        score = self._calculate_keyword_score(framework_content, self.FRAMEWORK_KEYWORDS)
        # Add more specific structural checks if necessary
        if "section 1" not in framework_content.lower() and "introduction" not in framework_content.lower():
            score *= 0.9 # Penalize if common starting sections are missing
        return round(score, 4)

    async def validate_assembly(self, assembly_content: str) -> float:
        """Validates the final assembled document."""
        score = self._calculate_keyword_score(assembly_content, self.ASSEMBLY_KEYWORDS)
        # Check for essential document structure
        content_lower = assembly_content.lower()
        if "table of contents" not in content_lower:
            score *= 0.8
        if "introduction" not in content_lower:
            score *= 0.9
        if "conclusion" not in content_lower and "summary" not in content_lower:
            score *= 0.9
        return round(score, 4)

    async def validate_procedures(self, procedures_content: str) -> float:
        """Validates the generated operational procedures."""
        score = self._calculate_keyword_score(procedures_content, self.PROCEDURES_KEYWORDS)
        if not re.search(r"step\s*\d+", procedures_content.lower()) and not re.search(r"\d\.", procedures_content.lower()):
            score *= 0.8 # Penalize if no numbered steps or similar list format found
        return round(score, 4)

    async def validate_tools(self, tools_content: str) -> float:
        """Validates the recommended tools and technologies."""
        score = self._calculate_keyword_score(tools_content, self.TOOLS_KEYWORDS)
        if "recommend" not in tools_content.lower() and "solution" not in tools_content.lower():
            score *= 0.9
        return round(score, 4)

    async def calculate_overall_score(self, framework_output, procedures_output, tools_output) -> float:
        """Calculates an overall quality score based on core stage outputs."""
        # Extract completeness scores from StageOutput objects
        framework_score = framework_output.completeness_score
        procedures_score = procedures_output.completeness_score
        tools_score = tools_output.completeness_score

        # Weighted average across the 3 core stages (matching test expectations)
        overall = (
            (framework_score * 0.25) +           # Framework: 25%
            (procedures_score * 0.5) +           # Procedures: 50%
            (tools_score * 0.25)                 # Tools: 25%
        )
        return round(overall, 4)

    def calculate_overall_score_extended(self, data_collection_score: float, risk_assessment_score: float,
                              framework_score: float, procedures_score: float, tools_score: float,
                              assembly_score: float = 0.0) -> float:
        """Calculates an overall quality score based on all individual stage scores."""
        # Weighted average across all 6 stages
        # Pre-processing stages have moderate weight, core stages have higher weight
        overall = (
            (data_collection_score * 0.15) +      # Data collection: 15%
            (risk_assessment_score * 0.15) +      # Risk assessment: 15%
            (framework_score * 0.25) +            # Framework: 25% (highest)
            (procedures_score * 0.25) +           # Procedures: 25% (highest)
            (tools_score * 0.15) +                # Tools: 15%
            (assembly_score * 0.05)               # Assembly: 5% (lowest, as it's mostly aggregation)
        )
        return round(overall, 4)
