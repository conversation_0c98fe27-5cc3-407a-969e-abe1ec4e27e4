"""
Shared models for the Enhanced Policy Generator system
Prevents circular imports between modules
"""

from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any


class GenerationStage(Enum):
    """Enumeration of policy generation stages"""
    DATA_COLLECTION = "data_collection"
    RISK_ASSESSMENT = "risk_assessment"
    FRAMEWORK = "framework"
    PROCEDURES = "procedures"
    TOOLS = "tools"
    ASSEMBLY = "assembly"


@dataclass
class PolicyRequest:
    """Request model for policy generation"""
    framework: str
    company_name: str
    company_type: str
    industry: str
    employee_count: int
    data_types: List[str]
    existing_policies: List[str]
    complexity_level: str
    user_id: str
    jurisdiction: str = "UK"
    risk_profile: str = "medium"


@dataclass
class StageOutput:
    """Output model for each generation stage"""
    content: str
    word_count: int
    completeness_score: float
    generation_time: float
    metadata: Dict[str, Any]


@dataclass
class PolicyDocument:
    """Complete policy document model"""
    policy_id: str
    content: Dict[str, StageOutput]
    metadata: Dict[str, Any]
    quality_metrics: Dict[str, float]
    generation_config: Dict[str, Any]
