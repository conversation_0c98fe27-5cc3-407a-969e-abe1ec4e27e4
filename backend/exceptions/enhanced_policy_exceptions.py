"""
Custom exception classes for the Enhanced Policy Generator Service.

This module provides a comprehensive hierarchy of exceptions for handling
various error scenarios in the policy generation pipeline with specific
error codes and recovery strategies.
"""

from typing import Optional, Dict, Any
import uuid
from datetime import datetime


class EnhancedPolicyException(Exception):
    """
    Base exception class for all Enhanced Policy Generator errors.
    
    Provides structured error handling with error codes, correlation IDs,
    and contextual information for debugging and recovery.
    """
    
    def __init__(
        self,
        message: str,
        error_code: str,
        correlation_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.context = context or {}
        self.original_exception = original_exception
        self.timestamp = datetime.utcnow().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging and API responses."""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "correlation_id": self.correlation_id,
            "context": self.context,
            "timestamp": self.timestamp,
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class AIServiceException(EnhancedPolicyException):
    """
    Exception for AI service communication failures.
    
    Raised when communication with external AI services (Google Gemini)
    fails due to network issues, API limits, or service unavailability.
    """
    
    def __init__(
        self,
        message: str,
        service_name: str = "google_gemini",
        http_status_code: Optional[int] = None,
        retry_after: Optional[int] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            error_code="AI_SERVICE_ERROR",
            **kwargs
        )
        self.service_name = service_name
        self.http_status_code = http_status_code
        self.retry_after = retry_after


class RateLimitException(AIServiceException):
    """
    Exception for AI service rate limiting.
    
    Raised when API rate limits are exceeded, includes retry-after information
    for implementing exponential backoff strategies.
    """
    
    def __init__(self, message: str, retry_after: int = 60, **kwargs):
        super().__init__(
            message=message,
            error_code="AI_RATE_LIMIT",
            retry_after=retry_after,
            **kwargs
        )


class AIRateLimitException(AIServiceException):
    """
    Exception for AI service rate limiting.
    
    Raised when API rate limits are exceeded, includes retry-after information
    for implementing exponential backoff strategies.
    """
    
    def __init__(self, message: str, retry_after: int = 60, **kwargs):
        super().__init__(
            message=message,
            error_code="AI_RATE_LIMIT",
            retry_after=retry_after,
            **kwargs
        )


class AIQuotaExceededException(AIServiceException):
    """
    Exception for AI service quota exhaustion.
    
    Raised when daily/monthly quotas are exceeded, typically requires
    different handling than rate limits.
    """
    
    def __init__(self, message: str, quota_type: str = "daily", **kwargs):
        super().__init__(
            message=message,
            error_code="AI_QUOTA_EXCEEDED",
            **kwargs
        )
        self.quota_type = quota_type


class FrameworkGenerationException(EnhancedPolicyException):
    """
    Exception for framework generation stage failures.
    
    Raised during the first stage of policy generation when creating
    the compliance framework structure.
    """
    
    def __init__(self, message: str, framework_type: str, **kwargs):
        super().__init__(
            message=message,
            error_code="FRAMEWORK_GENERATION_ERROR",
            **kwargs
        )
        self.framework_type = framework_type


class ProcedureGenerationException(EnhancedPolicyException):
    """
    Exception for procedure generation stage failures.
    
    Raised during the second stage when generating detailed procedures
    based on the framework.
    """
    
    def __init__(self, message: str, procedure_type: str, **kwargs):
        super().__init__(
            message=message,
            error_code="PROCEDURE_GENERATION_ERROR",
            **kwargs
        )
        self.procedure_type = procedure_type


class ToolGenerationException(EnhancedPolicyException):
    """
    Exception for tool generation stage failures.
    
    Raised during the third stage when generating supporting tools
    and templates.
    """
    
    def __init__(self, message: str, tool_type: str, **kwargs):
        super().__init__(
            message=message,
            error_code="TOOL_GENERATION_ERROR",
            **kwargs
        )
        self.tool_type = tool_type


class ContentException(EnhancedPolicyException):
    """
    Exception for issues with generated content.
    
    Raised when AI-generated content is empty, malformed, or fails to meet
    basic structural requirements before detailed validation.
    """
    
    def __init__(self, message: str, content_type: str, **kwargs):
        super().__init__(
            message=message,
            error_code="CONTENT_ERROR",
            **kwargs
        )
        self.content_type = content_type


class AssemblyException(EnhancedPolicyException):
    """
    Exception for failures during the final document assembly.
    
    Raised when the process of combining generated stages into a coherent
    policy document encounters errors.
    """
    
    def __init__(self, message: str, assembly_stage: str, **kwargs):
        super().__init__(
            message=message,
            error_code="ASSEMBLY_ERROR",
            **kwargs
        )
        self.assembly_stage = assembly_stage


class QualityException(EnhancedPolicyException):
    """
    Exception for policy quality failures.
    
    Raised when the generated policy or its components fail to meet
    defined quality standards or completeness scores.
    """
    
    def __init__(self, message: str, quality_metric: str, score: float, **kwargs):
        super().__init__(
            message=message,
            error_code="QUALITY_ERROR",
            **kwargs
        )
        self.quality_metric = quality_metric
        self.score = score


class DocumentAssemblyException(EnhancedPolicyException):
    """
    Exception for document assembly stage failures.
    
    Raised during the final stage when assembling all components
    into the final policy document.
    """
    
    def __init__(self, message: str, assembly_phase: str = "final", **kwargs):
        super().__init__(
            message=message,
            error_code="DOCUMENT_ASSEMBLY_ERROR",
            **kwargs
        )
        self.assembly_phase = assembly_phase


class ValidationException(EnhancedPolicyException):
    """
    Exception for policy validation failures.
    
    Raised when generated policies fail quality validation checks
    or compliance requirements.
    """
    
    def __init__(
        self,
        message: str,
        validation_type: str,
        failed_checks: Optional[list] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            **kwargs
        )
        self.validation_type = validation_type
        self.failed_checks = failed_checks or []


class TimeoutException(EnhancedPolicyException):
    """
    Exception for operation timeouts.
    
    Raised when operations exceed the configured timeout limits,
    particularly important for the <120 second generation target.
    """
    
    def __init__(
        self,
        message: str,
        operation: str,
        timeout_seconds: int,
        elapsed_seconds: float,
        **kwargs
    ):
        super().__init__(
            message=message,
            error_code="OPERATION_TIMEOUT",
            **kwargs
        )
        self.operation = operation
        self.timeout_seconds = timeout_seconds
        self.elapsed_seconds = elapsed_seconds


class ConfigurationException(EnhancedPolicyException):
    """
    Exception for configuration errors.
    
    Raised when required configuration is missing or invalid,
    preventing proper service initialization or operation.
    """
    
    def __init__(
        self,
        message: str,
        config_key: str,
        config_value: Any = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            **kwargs
        )
        self.config_key = config_key
        self.config_value = config_value


class DatabaseException(EnhancedPolicyException):
    """
    Exception for database operation failures.
    
    Raised when interactions with the database (MongoDB) fail due to
    connection issues, invalid queries, or data integrity problems.
    """
    
    def __init__(self, message: str, db_operation: str, **kwargs):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            **kwargs
        )
        self.db_operation = db_operation


# Exception mapping for different error scenarios
EXCEPTION_MAPPING = {
    "framework": FrameworkGenerationException,
    "procedures": ProcedureGenerationException,
    "tools": ToolGenerationException,
    "assembly": DocumentAssemblyException,
    "validation": ValidationException,
    "ai_service": AIServiceException,
    "rate_limit": RateLimitException,
    "quota": AIQuotaExceededException,
    "timeout": TimeoutException,
    "config": ConfigurationException,
    "database": DatabaseException,
    "content": ContentException, # Added to mapping
    "assembly": AssemblyException, # Added to mapping
    "quality": QualityException # Added to mapping
}


def create_exception(
    exception_type: str,
    message: str,
    **kwargs
) -> EnhancedPolicyException:
    """
    Factory function for creating appropriate exception instances.
    
    Args:
        exception_type: The type of exception to create
        message: Error message
        **kwargs: Additional parameters for the specific exception type
    
    Returns:
        An instance of the appropriate exception class
    
    Raises:
        ValueError: If exception_type is not recognized
    """
    if exception_type not in EXCEPTION_MAPPING:
        raise ValueError(f"Unknown exception type: {exception_type}")
    
    exception_class = EXCEPTION_MAPPING[exception_type]
    return exception_class(message=message, **kwargs)