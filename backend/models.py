### Begin multi-line content ###
"""
ComplianceGPT Data Models

Defines the data structures used throughout the ComplianceGPT platform.
"""

from pydantic import BaseModel, Field, EmailStr, field_validator # Changed validator to field_validator
import re # Added re for custom validation
from typing import List, Optional, Dict, Any, Annotated
from datetime import datetime
from enum import Enum

class ComplianceFrameworkType(str, Enum):
    """Supported compliance frameworks"""
    GDPR = "gdpr"
    SOC2 = "soc2" 
    ISO27001 = "iso27001"

class PolicyStatus(str, Enum):
    """Policy status options"""
    DRAFT = "draft"
    REVIEW = "review"
    APPROVED = "approved"
    ARCHIVED = "archived"

class ProjectStatus(str, Enum):
    """Project status options"""
    PLANNING = "planning"
    IMPLEMENTING = "implementing"
    READY = "ready"
    AUDITING = "auditing"
    COMPLETE = "complete"

class ComplianceFramework(BaseModel):
    """Compliance framework definition"""
    id: str
    name: str
    description: str
    controls: List[Dict[str, Any]]
    requirements: List[str]

class PolicyGenerationRequest(BaseModel):
    """Request model for AI policy generation"""
    framework: ComplianceFrameworkType
    company_name: str = Field(..., min_length=1, max_length=100, pattern="^[a-zA-Z0-9 .,'_-]+$")
    company_type: str = Field(..., min_length=2, max_length=100, description="Type of the company, e.g., SaaS, E-commerce", pattern="^[a-zA-Z0-9 .,'_-]+$")
    employee_count: int = Field(gt=0, le=10000)
    industry: str = Field(..., min_length=1, max_length=100, pattern="^[a-zA-Z0-9 .,'_-]+$")
    data_types: List[Annotated[str, Field(min_length=3, max_length=500, description="Types of data processed, e.g., PII, Financial")]] = []
    existing_policies: List[Annotated[str, Field(min_length=3, max_length=500, description="Names or brief descriptions of existing policies")]] = []
    complexity_level: str = Field(default="standard", pattern="^(basic|standard|advanced)$")

class Policy(BaseModel):
    """Policy data model"""
    id: str
    title: str = Field(..., min_length=3, max_length=255, pattern="^[\w\s.,'\"()?!-]+$")
    content: str = Field(..., min_length=10)
    framework: ComplianceFrameworkType
    company_name: str = Field(..., min_length=1, max_length=100, pattern="^[a-zA-Z0-9 .,'_-]+$")
    ai_generated: bool = True
    thinking_mode: bool = False
    version: int = 1
    status: PolicyStatus = PolicyStatus.DRAFT
    created_at: datetime
    updated_at: datetime

class ComplianceProject(BaseModel):
    """Compliance project data model"""
    id: str
    company_name: str = Field(..., min_length=1, max_length=100)
    framework: ComplianceFrameworkType
    status: ProjectStatus = ProjectStatus.PLANNING
    progress: int = Field(ge=0, le=100, default=0)
    target_date: Optional[datetime] = None
    policies: List[Annotated[str, Field(min_length=1)]] = []
    evidence_collected: int = 0
    evidence_required: int = 0
    created_at: datetime

class DashboardStats(BaseModel):
    """Dashboard statistics model"""
    total_projects: int
    total_policies: int
    average_progress: float
    frameworks_supported: int
    total_integrations: int = 0
    evidence_collected: int = 0

class DashboardData(BaseModel):
    """Complete dashboard data model"""
    stats: DashboardStats
    recent_projects: List[ComplianceProject]
    recent_policies: List[Policy]
    frameworks: List[ComplianceFramework]

# Evidence collection models
class EvidenceRequest(BaseModel):
    """Request model for evidence collection"""
    integration_id: str = Field(..., min_length=1, pattern="^[a-zA-Z0-9_-]+$")
    evidence_types: Optional[List[Annotated[str, Field(min_length=1)]]] = None
    framework: Optional[ComplianceFrameworkType] = None

class IntegrationSetupRequest(BaseModel):
    """Request model for setting up integrations"""
    provider: str = Field(..., min_length=1, max_length=50, pattern="^[a-zA-Z0-9_-]+$")
    name: str = Field(..., min_length=1, max_length=100, pattern="^[a-zA-Z0-9 .,'_-]+$")
    credentials: Dict[str, Any]
    settings: Dict[str, Any] = {}

# User Management Models
class UserSignupRequest(BaseModel):
    fullName: str = Field(..., min_length=2, max_length=100, description="User's full name", pattern="^[a-zA-Z ]+$")
    companyName: str = Field(..., min_length=2, max_length=100, description="User's company name", pattern="^[a-zA-Z0-9 .,'_-]+$")
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(
        ..., 
        min_length=8, 
        max_length=100, 
        description="User's password. Must be 8-100 characters, include uppercase, lowercase, digit, and special character."
        # pattern removed, custom validator added below
    )

    @field_validator('password')
    @classmethod
    def validate_password_complexity(cls, value: str) -> str:
        PASSWORD_PATTERN = r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+-=[\]{};':\",./<>?]).*$"
        if not re.match(PASSWORD_PATTERN, value):
            raise ValueError(
                'Password must be 8-100 characters and include at least one uppercase letter, '
                'one lowercase letter, one digit, and one special character.'
            )
        return value

class UserLoginRequest(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=100)

class User(BaseModel):
    id: str
    fullName: str
    companyName: str
    email: EmailStr
    subscription_plan: str = Field(default="free")
    credits: int = Field(default=10)
    settings: Dict[str, Any] = Field(default_factory=lambda: {"theme": "light", "notifications_enabled": True})
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None


class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: User


class ProjectCreate(BaseModel):
    """Request model for creating a new compliance project"""
    company_name: str = Field(..., min_length=1, max_length=100, pattern="^[a-zA-Z0-9 .,'_-]+$")
    framework: ComplianceFrameworkType
    target_date: Optional[str] = Field(None, description="Target completion date in ISO format, e.g., YYYY-MM-DD")

    @field_validator('target_date', mode='before')
    @classmethod
    def validate_target_date_format(cls, value: Optional[str]) -> Optional[str]:
        if value is None:
            return value
        try:
            # datetime is already imported at the top of models.py
            datetime.fromisoformat(value.replace('Z', '+00:00')) # Handles 'Z' for UTC
            return value
        except ValueError:
            # Try parsing just date if full ISO format fails (e.g., YYYY-MM-DD)
            try:
                datetime.strptime(value, '%Y-%m-%d')
                return value
            except ValueError:
                raise ValueError("Invalid target_date format. Please use YYYY-MM-DD or full ISO format (YYYY-MM-DDTHH:MM:SSZ).")
### End multi-line content ###