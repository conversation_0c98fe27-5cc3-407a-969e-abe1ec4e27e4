"""
Google Workspace OAuth Handler for ComplianceGPT
Implements secure OAuth 2.0 flow for Google Workspace integration
"""

import os
import json
import base64
from typing import Dict, Optional
from urllib.parse import urlencode
import aiohttp
from datetime import datetime, timedelta

class GoogleOAuthHandler:
    """Handle Google Workspace OAuth authentication"""
    
    def __init__(self):
        self.client_id = os.getenv('GOOGLE_CLIENT_ID')
        self.client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
        self.redirect_uri = os.getenv('GOOGLE_REDIRECT_URI', 'http://localhost:3000/oauth/google/callback')
        
        self.auth_url = 'https://accounts.google.com/o/oauth2/v2/auth'
        self.token_url = 'https://oauth2.googleapis.com/token'
        
        # Required scopes for compliance evidence collection
        self.scopes = [
            'https://www.googleapis.com/auth/admin.reports.audit.readonly',
            'https://www.googleapis.com/auth/admin.directory.user.readonly',
            'https://www.googleapis.com/auth/admin.directory.group.readonly',
            'https://www.googleapis.com/auth/drive.metadata.readonly',
            'https://www.googleapis.com/auth/admin.directory.domain.readonly'
        ]
    
    def get_authorization_url(self, state: str = None) -> str:
        """Generate OAuth authorization URL"""
        params = {
            'client_id': self.client_id,
            'redirect_uri': self.redirect_uri,
            'scope': ' '.join(self.scopes),
            'response_type': 'code',
            'access_type': 'offline',
            'prompt': 'consent'
        }
        
        if state:
            params['state'] = state
            
        return f"{self.auth_url}?{urlencode(params)}"
    
    async def exchange_code_for_token(self, code: str) -> Dict[str, any]:
        """Exchange authorization code for access tokens"""
        token_data = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': self.redirect_uri
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(self.token_url, data=token_data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Token exchange failed: {error_text}")
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, any]:
        """Refresh expired access token"""
        token_data = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'refresh_token': refresh_token,
            'grant_type': 'refresh_token'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(self.token_url, data=token_data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Token refresh failed: {error_text}")
    
    async def get_user_info(self, access_token: str) -> Dict[str, any]:
        """Get user information to validate token"""
        headers = {'Authorization': f'Bearer {access_token}'}
        
        async with aiohttp.ClientSession() as session:
            async with session.get('https://www.googleapis.com/oauth2/v2/userinfo', headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"User info fetch failed: {error_text}")
    
    def is_token_expired(self, token_data: Dict[str, any]) -> bool:
        """Check if access token is expired"""
        if 'expires_at' not in token_data:
            return True
            
        expires_at = datetime.fromisoformat(token_data['expires_at'])
        return datetime.utcnow() >= expires_at
    
    def calculate_expiry(self, expires_in: int) -> str:
        """Calculate token expiry time"""
        expiry = datetime.utcnow() + timedelta(seconds=expires_in)
        return expiry.isoformat()
    
    async def validate_domain_access(self, access_token: str, required_domain: str) -> bool:
        """Validate that the user has admin access to the required domain"""
        headers = {'Authorization': f'Bearer {access_token}'}
        
        try:
            async with aiohttp.ClientSession() as session:
                # Check if user can access admin directory
                async with session.get(
                    f'https://admin.googleapis.com/admin/directory/v1/users?domain={required_domain}&maxResults=1',
                    headers=headers
                ) as response:
                    return response.status == 200
        except:
            return False