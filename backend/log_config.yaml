version: 1
disable_existing_loggers: False
formatters:
  default:
    (): uvicorn.logging.DefaultFormatter
    fmt: "%(levelprefix)s %(asctime)s - %(name)s - %(filename)s:%(lineno)d - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  access:
    (): uvicorn.logging.AccessFormatter
    fmt: "%(levelprefix)s %(asctime)s - %(client_addr)s - '%(request_line)s' %(status_code)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
handlers:
  default:
    formatter: default
    class: logging.StreamHandler
    stream: ext://sys.stderr
  access:
    formatter: access
    class: logging.StreamHandler
    stream: ext://sys.stdout
  # Handler for our application's logger
  compliance_app_handler:
    formatter: default
    class: logging.StreamHandler
    stream: ext://sys.stderr # Or a file, e.g., file:///path/to/app.log
loggers:
  uvicorn:
    handlers:
      - default
    level: INFO
    propagate: no
  uvicorn.error:
    level: INFO
  uvicorn.access:
    handlers:
      - access
    level: INFO
    propagate: no
  # Our application's logger
  compliance_app:
    handlers:
      - compliance_app_handler # Use the dedicated handler
    level: DEBUG # Ensure DEBUG level messages are processed
    propagate: no # Prevent duplication if root logger also has a handler
root:
  level: INFO
  handlers:
    - default
