"""
Word Document Generator for ComplianceGPT
Professional Word documents for compliance policies
"""

from typing import Dict, Any
from datetime import datetime
import io
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

class WordReportGenerator:
    """Generate professional Word documents for compliance policies"""
    
    def __init__(self):
        pass
    
    def generate_policy_word(
        self,
        policy_data: Dict[str, Any],
        company_data: Dict[str, Any]
    ) -> bytes:
        """Generate branded Word document for policy"""
        
        doc = Document()
        
        # Set up document styles
        self._setup_styles(doc)
        
        # Add company header with branding
        self._add_header(doc, company_data, policy_data)
        
        # Add policy content
        self._add_policy_content(doc, policy_data)
        
        # Add footer
        self._add_footer(doc, company_data)
        
        # Save to bytes
        buffer = io.BytesIO()
        doc.save(buffer)
        buffer.seek(0)
        return buffer.getvalue()
    
    def _setup_styles(self, doc):
        """Setup custom styles for the document"""
        
        # Company Title Style
        styles = doc.styles
        
        try:
            title_style = styles.add_style('CompanyTitle', WD_STYLE_TYPE.PARAGRAPH)
            title_style.font.name = 'Arial'
            title_style.font.size = Pt(24)
            title_style.font.bold = True
            title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title_style.paragraph_format.space_after = Pt(20)
        except:
            pass  # Style might already exist
        
        try:
            subtitle_style = styles.add_style('PolicySubtitle', WD_STYLE_TYPE.PARAGRAPH)
            subtitle_style.font.name = 'Arial'
            subtitle_style.font.size = Pt(16)
            subtitle_style.font.bold = True
            subtitle_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
            subtitle_style.paragraph_format.space_after = Pt(15)
        except:
            pass
        
        try:
            header_style = styles.add_style('PolicyHeader', WD_STYLE_TYPE.PARAGRAPH)
            header_style.font.name = 'Arial'
            header_style.font.size = Pt(14)
            header_style.font.bold = True
            header_style.paragraph_format.space_before = Pt(12)
            header_style.paragraph_format.space_after = Pt(6)
        except:
            pass
    
    def _add_header(self, doc, company_data: Dict[str, Any], policy_data: Dict[str, Any]):
        """Add company-branded header"""
        
        # Company name and logo section
        header_para = doc.add_paragraph()
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add company logo if provided
        if company_data.get('logo'):
            try:
                header_para.add_run().add_picture(company_data['logo'], width=Inches(2))
                header_para.add_run('\n')
            except:
                pass  # Logo loading failed, continue without
        
        # Company name
        company_run = header_para.add_run(company_data.get('name', 'Company'))
        company_run.font.name = 'Arial'
        company_run.font.size = Pt(24)
        company_run.font.bold = True
        
        # Add separator line
        doc.add_paragraph('_' * 80).alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Policy title
        title_para = doc.add_paragraph()
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_run = title_para.add_run(policy_data.get('title', 'Compliance Policy'))
        title_run.font.name = 'Arial'
        title_run.font.size = Pt(18)
        title_run.font.bold = True
        
        # Framework and date info
        info_para = doc.add_paragraph()
        info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        info_run = info_para.add_run(
            f"Framework: {policy_data.get('framework', '').upper()} | "
            f"Version: {policy_data.get('version', 1)} | "
            f"Date: {datetime.now().strftime('%B %d, %Y')}"
        )
        info_run.font.name = 'Arial'
        info_run.font.size = Pt(12)
        
        # Add spacing
        doc.add_paragraph()
    
    def _add_policy_content(self, doc, policy_data: Dict[str, Any]):
        """Add formatted policy content"""
        
        content = policy_data.get('content', '')
        
        # Split content into sections
        sections = content.split('\n\n')
        
        for section in sections:
            if not section.strip():
                continue
            
            section = section.strip()
            
            # Check if this is a header (starts with ** or #)
            if section.startswith('**') and section.endswith('**'):
                # Bold header
                header_text = section.replace('**', '')
                para = doc.add_paragraph()
                run = para.add_run(header_text)
                run.font.bold = True
                run.font.size = Pt(14)
                para.paragraph_format.space_before = Pt(12)
                para.paragraph_format.space_after = Pt(6)
            
            elif section.startswith('##'):
                # Markdown-style header
                header_text = section.replace('##', '').strip()
                para = doc.add_paragraph()
                run = para.add_run(header_text)
                run.font.bold = True
                run.font.size = Pt(14)
                para.paragraph_format.space_before = Pt(12)
                para.paragraph_format.space_after = Pt(6)
            
            elif section.startswith('#'):
                # Main header
                header_text = section.replace('#', '').strip()
                para = doc.add_paragraph()
                run = para.add_run(header_text)
                run.font.bold = True
                run.font.size = Pt(16)
                para.paragraph_format.space_before = Pt(15)
                para.paragraph_format.space_after = Pt(8)
            
            elif section.startswith('*   ') or section.startswith('- '):
                # Bullet point
                bullet_text = section.replace('*   ', '').replace('- ', '').strip()
                para = doc.add_paragraph(bullet_text, style='List Bullet')
                para.paragraph_format.left_indent = Inches(0.5)
            
            else:
                # Regular paragraph
                para = doc.add_paragraph(section)
                para.paragraph_format.space_after = Pt(6)
                para.paragraph_format.line_spacing = 1.15
    
    def _add_footer(self, doc, company_data: Dict[str, Any]):
        """Add company footer"""
        
        # Add page break before footer
        doc.add_page_break()
        
        # Footer content
        footer_para = doc.add_paragraph()
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        footer_text = (
            f"This document was generated by ComplianceGPT for {company_data.get('name', 'Company')} "
            f"on {datetime.now().strftime('%B %d, %Y')}.\n\n"
            "ComplianceGPT - AI-Powered Compliance Automation for UK SMBs\n"
            "Democratizing enterprise-grade compliance at 1/5th the cost."
        )
        
        footer_run = footer_para.add_run(footer_text)
        footer_run.font.name = 'Arial'
        footer_run.font.size = Pt(10)
        footer_run.font.italic = True
        
        # Add separator
        separator_para = doc.add_paragraph('_' * 60)
        separator_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        separator_run = separator_para.runs[0]
        separator_run.font.size = Pt(8)
        
        # Contact information
        contact_para = doc.add_paragraph()
        contact_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        contact_run = contact_para.add_run(
            "For questions about this policy or ComplianceGPT, "
            "please visit our platform or contact your compliance administrator."
        )
        contact_run.font.name = 'Arial'
        contact_run.font.size = Pt(9)