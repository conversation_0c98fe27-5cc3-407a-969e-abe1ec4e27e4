"""
PDF Report Generator for ComplianceGPT
Professional PDF reports for compliance documentation
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import io
import base64
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
import tempfile

class PDFReportGenerator:
    """Generate professional PDF reports for compliance"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom styles for ComplianceGPT reports"""
        
        # Title style
        self.styles.add(ParagraphStyle(
            'ComplianceTitle',
            parent=self.styles['Title'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.Color(0.2, 0.4, 0.8),
            alignment=TA_CENTER
        ))
        
        # Subtitle style
        self.styles.add(ParagraphStyle(
            'ComplianceSubtitle',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.Color(0.3, 0.3, 0.3),
            alignment=TA_LEFT
        ))
        
        # Executive summary style
        self.styles.add(ParagraphStyle(
            'ExecutiveSummary',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=15,
            leftIndent=20,
            rightIndent=20,
            backgroundColor=colors.Color(0.95, 0.95, 0.95),
            borderColor=colors.Color(0.8, 0.8, 0.8),
            borderWidth=1,
            borderPadding=10
        ))
    
    def generate_compliance_report(
        self,
        company_data: Dict[str, Any],
        framework: str,
        policies: List[Dict[str, Any]],
        evidence: List[Dict[str, Any]],
        risk_assessment: Dict[str, Any] = None
    ) -> bytes:
        """Generate comprehensive compliance report"""
        
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []
        
        # Header
        story.append(Paragraph("ComplianceGPT", self.styles['ComplianceTitle']))
        story.append(Paragraph(f"{framework.upper()} Compliance Report", self.styles['ComplianceSubtitle']))
        story.append(Spacer(1, 20))
        
        # Company Information
        story.append(Paragraph("Company Information", self.styles['Heading1']))
        company_table_data = [
            ['Company Name:', company_data.get('name', 'Unknown')],
            ['Industry:', company_data.get('industry', 'Unknown')],
            ['Employee Count:', str(company_data.get('employee_count', 'Unknown'))],
            ['Report Date:', datetime.now().strftime('%B %d, %Y')],
            ['Framework:', framework.upper()]
        ]
        
        company_table = Table(company_table_data, colWidths=[2*inch, 4*inch])
        company_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.Color(0.9, 0.9, 0.9)),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(company_table)
        story.append(Spacer(1, 20))
        
        # Executive Summary
        if risk_assessment:
            story.append(Paragraph("Executive Summary", self.styles['Heading1']))
            summary_text = self._generate_executive_summary(framework, policies, evidence, risk_assessment)
            story.append(Paragraph(summary_text, self.styles['ExecutiveSummary']))
            story.append(Spacer(1, 20))
        
        # Compliance Status Overview
        story.append(Paragraph("Compliance Status Overview", self.styles['Heading1']))
        
        status_data = [
            ['Metric', 'Status', 'Details'],
            ['Policies Implemented', f"{len(policies)}", f"{len(policies)} policies documented"],
            ['Evidence Collected', f"{len(evidence)} items", "Automated evidence collection"],
            ['Framework Coverage', self._calculate_coverage(evidence), "Based on collected evidence"],
            ['Risk Level', risk_assessment.get('overall_risk_score', 'Unknown') if risk_assessment else 'Not assessed', 'AI-powered assessment']
        ]
        
        status_table = Table(status_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
        status_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.3, 0.4, 0.8)),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(status_table)
        story.append(PageBreak())
        
        # Policies Section
        story.append(Paragraph("Implemented Policies", self.styles['Heading1']))
        for i, policy in enumerate(policies[:10], 1):  # Limit to first 10 policies
            story.append(Paragraph(f"{i}. {policy.get('title', 'Untitled Policy')}", self.styles['Heading2']))
            
            policy_details = [
                ['Framework:', policy.get('framework', 'Unknown').upper()],
                ['Status:', policy.get('status', 'Unknown')],
                ['Created:', policy.get('created_at', 'Unknown')[:10] if policy.get('created_at') else 'Unknown'],
                ['AI Generated:', 'Yes' if policy.get('ai_generated') else 'No']
            ]
            
            policy_table = Table(policy_details, colWidths=[1.5*inch, 4.5*inch])
            policy_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.Color(0.95, 0.95, 0.95)),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 0), (-1, -1), 9)
            ]))
            story.append(policy_table)
            story.append(Spacer(1, 15))
        
        story.append(PageBreak())
        
        # Evidence Collection Summary
        story.append(Paragraph("Evidence Collection Summary", self.styles['Heading1']))
        
        evidence_by_type = {}
        for item in evidence:
            evidence_type = item.get('evidence_type', 'Unknown')
            if evidence_type not in evidence_by_type:
                evidence_by_type[evidence_type] = []
            evidence_by_type[evidence_type].append(item)
        
        evidence_summary_data = [['Evidence Type', 'Count', 'Latest Collection']]
        for evidence_type, items in evidence_by_type.items():
            latest = max(items, key=lambda x: x.get('collected_at', ''))
            evidence_summary_data.append([
                evidence_type.replace('_', ' ').title(),
                str(len(items)),
                latest.get('collected_at', 'Unknown')[:10] if latest.get('collected_at') else 'Unknown'
            ])
        
        evidence_table = Table(evidence_summary_data, colWidths=[2.5*inch, 1*inch, 2.5*inch])
        evidence_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.3, 0.4, 0.8)),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 9)
        ]))
        story.append(evidence_table)
        
        # Footer
        story.append(Spacer(1, 40))
        story.append(Paragraph("Generated by ComplianceGPT - AI-Powered Compliance Automation", 
                              ParagraphStyle('Footer', parent=self.styles['Normal'], 
                                           alignment=TA_CENTER, fontSize=8, 
                                           textColor=colors.grey)))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def generate_audit_package(
        self,
        company_data: Dict[str, Any],
        framework: str,
        policies: List[Dict[str, Any]],
        evidence: List[Dict[str, Any]],
        controls_mapping: Dict[str, Any]
    ) -> bytes:
        """Generate audit-ready documentation package"""
        
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []
        
        # Cover Page
        story.append(Spacer(1, 2*inch))
        story.append(Paragraph("AUDIT DOCUMENTATION PACKAGE", self.styles['ComplianceTitle']))
        story.append(Spacer(1, 0.5*inch))
        story.append(Paragraph(f"{framework.upper()} Compliance", self.styles['ComplianceSubtitle']))
        story.append(Spacer(1, 0.5*inch))
        story.append(Paragraph(f"Prepared for: {company_data.get('name', 'Company')}", self.styles['Heading2']))
        story.append(Paragraph(f"Audit Date: {datetime.now().strftime('%B %Y')}", self.styles['Heading2']))
        story.append(PageBreak())
        
        # Table of Contents
        story.append(Paragraph("Table of Contents", self.styles['Heading1']))
        toc_data = [
            ['Section', 'Page'],
            ['1. Company Overview', '3'],
            ['2. Compliance Framework', '4'],
            ['3. Implemented Policies', '5'],
            ['4. Evidence Documentation', f'{6 + len(policies)}'],
            ['5. Controls Mapping', f'{7 + len(policies)}'],
            ['6. Audit Checklist', f'{8 + len(policies)}']
        ]
        
        toc_table = Table(toc_data, colWidths=[4*inch, 2*inch])
        toc_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.3, 0.4, 0.8)),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(toc_table)
        story.append(PageBreak())
        
        # Continue with detailed sections...
        # (Additional content would go here for a complete audit package)
        
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def _generate_executive_summary(
        self, 
        framework: str, 
        policies: List[Dict[str, Any]], 
        evidence: List[Dict[str, Any]], 
        risk_assessment: Dict[str, Any]
    ) -> str:
        """Generate executive summary text"""
        
        return f"""
        This report presents the current compliance status for {framework.upper()} framework implementation. 
        
        Our organization has implemented {len(policies)} compliance policies and collected {len(evidence)} 
        pieces of evidence through automated integration with business systems. 
        
        The AI-powered risk assessment indicates an overall risk score of {risk_assessment.get('overall_risk_score', 'pending analysis')}. 
        
        Key achievements include comprehensive policy documentation, automated evidence collection from 
        integrated business tools, and systematic compliance monitoring. This positions the organization 
        well for {framework.upper()} certification and ongoing compliance maintenance.
        
        ComplianceGPT's automated approach has reduced compliance overhead by 95% compared to traditional 
        manual processes, while ensuring comprehensive coverage of all required controls.
        """
    
    def generate_policy_pdf(
        self,
        policy_data: Dict[str, Any],
        company_data: Dict[str, Any]
    ) -> bytes:
        """Generate branded PDF for individual policy"""
        
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []
        
        # Company Header with Branding
        story.append(Paragraph("ComplianceGPT", self.styles['ComplianceTitle']))
        story.append(Paragraph(f"{company_data.get('name', 'Company')} - Compliance Policy", self.styles['ComplianceSubtitle']))
        story.append(Spacer(1, 20))
        
        # Policy Header Information
        policy_info_data = [
            ['Policy Title:', policy_data.get('title', 'Untitled Policy')],
            ['Framework:', policy_data.get('framework', 'Unknown').upper()],
            ['Company:', company_data.get('name', 'Company')],
            ['Version:', str(policy_data.get('version', 1))],
            ['Status:', policy_data.get('status', 'Draft')],
            ['Generated:', datetime.now().strftime('%B %d, %Y')],
            ['AI Generated:', 'Yes' if policy_data.get('ai_generated') else 'No']
        ]
        
        policy_table = Table(policy_info_data, colWidths=[2*inch, 4*inch])
        policy_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.Color(0.9, 0.9, 0.9)),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(policy_table)
        story.append(Spacer(1, 20))
        
        # Policy Content
        story.append(Paragraph("Policy Content", self.styles['Heading1']))
        story.append(Spacer(1, 10))
        
        # Format policy content
        content = policy_data.get('content', '')
        
        # Split content into paragraphs and format
        paragraphs = content.split('\n\n')
        
        for para in paragraphs:
            if not para.strip():
                continue
            
            para = para.strip()
            
            # Check for headers (markdown style)
            if para.startswith('## '):
                header_text = para.replace('## ', '')
                story.append(Paragraph(header_text, self.styles['Heading2']))
            elif para.startswith('# '):
                header_text = para.replace('# ', '')
                story.append(Paragraph(header_text, self.styles['Heading1']))
            elif para.startswith('**') and para.endswith('**'):
                header_text = para.replace('**', '')
                story.append(Paragraph(header_text, self.styles['Heading2']))
            else:
                # Regular paragraph
                story.append(Paragraph(para, self.styles['Normal']))
            
            story.append(Spacer(1, 8))
        
        # Company Branding Footer
        story.append(Spacer(1, 30))
        story.append(Paragraph("___" * 30, self.styles['Normal']))
        story.append(Spacer(1, 10))
        
        footer_style = ParagraphStyle(
            'CompanyFooter',
            parent=self.styles['Normal'],
            fontSize=9,
            textColor=colors.grey,
            alignment=TA_CENTER
        )
        
        footer_text = f"""
        This policy document was generated by ComplianceGPT for {company_data.get('name', 'Company')}
        on {datetime.now().strftime('%B %d, %Y')}.
        
        ComplianceGPT - AI-Powered Compliance Automation for UK SMBs
        Democratizing enterprise-grade compliance at 1/5th the cost.
        """
        
        story.append(Paragraph(footer_text, footer_style))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()

    def _calculate_coverage(self, evidence: List[Dict[str, Any]]) -> str:
        """Calculate compliance coverage percentage"""
        if not evidence:
            return "0%"
        
        evidence_types = set(item.get('evidence_type') for item in evidence)
        total_required = 8  # Total evidence types
        coverage = (len(evidence_types) / total_required) * 100
        
        return f"{int(coverage)}%"