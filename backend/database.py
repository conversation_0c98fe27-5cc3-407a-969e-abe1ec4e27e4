import motor.motor_asyncio
from .config import M<PERSON><PERSON><PERSON>_URL, DB_NAME # Assuming config.py is in the same 'backend' directory

# MongoDB setup
# Ensure MONGO_URL is correctly loaded and includes the database name if necessary,
# or use DB_NAME if your MONGO_URL is just the server address.
# For Atlas, MONGO_URL usually contains the db name or you connect to client and then get_database(DB_NAME)

client = motor.motor_asyncio.AsyncIOMotorClient(MONGO_URL)

# If DB_NAME is intended to select the database from the client:
db = client[DB_NAME]

# If MONGO_URL already specifies the database (common with some connection strings):
# db = client.get_default_database() 
# Ensure your MONGO_URL is structured appropriately if using get_default_database(),
# or that DB_NAME is correctly used as above.

# The most common pattern if MONGO_URL is like mongodb://.../mydatabase
# is that client.get_default_database() would work if mydatabase is in the URI.
# If MONGO_URL is just mongodb://... and DB_NAME is 'mydatabase', then client[DB_NAME] is correct.
# Given the existing setup in server.py was client.compliancegpt, and DB_NAME is likely 'compliancegpt',
# client[DB_NAME] is the most direct replacement.

async def get_db() -> motor.motor_asyncio.AsyncIOMotorDatabase:
    return db
