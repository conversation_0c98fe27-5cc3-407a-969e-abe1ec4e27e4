#!/usr/bin/env python3
"""
Test script to verify that the correct Gemini model is configured
"""

import sys
import os

def test_config_models():
    """Test that config.py has the correct models"""
    try:
        from config import MODELS
        print("📋 Current model configuration:")
        for key, model in MODELS.items():
            print(f"  {key}: {model}")
        
        # Check if the correct model is configured
        expected_model = "gemini-2.5-flash-preview-05-20"
        
        if MODELS["basic"] == expected_model:
            print(f"✅ Basic model correctly set to: {expected_model}")
        else:
            print(f"❌ Basic model incorrect. Expected: {expected_model}, Got: {MODELS['basic']}")
            return False
            
        if MODELS["thinking"] == expected_model:
            print(f"✅ Thinking model correctly set to: {expected_model}")
        else:
            print(f"❌ Thinking model incorrect. Expected: {expected_model}, Got: {MODELS['thinking']}")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def test_enhanced_generator_model():
    """Test that enhanced policy generator uses correct model"""
    try:
        from services.enhanced_policy_generator import AIService
        from config import GEMINI_API_KEY
        
        # Create AIService instance (without API key for testing)
        ai_service = AIService("test-key")
        
        # Check the model name
        model_name = ai_service.model.model_name
        expected_model = "gemini-2.5-flash-preview-05-20"
        
        if model_name == expected_model:
            print(f"✅ Enhanced Policy Generator using correct model: {model_name}")
            return True
        else:
            print(f"❌ Enhanced Policy Generator using wrong model. Expected: {expected_model}, Got: {model_name}")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced generator model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_engine_references():
    """Test that AI engine files reference correct model"""
    try:
        from ai_engine.compliance_advisor import ComplianceAdvisor
        from ai_engine.risk_assessor import AIRiskAssessor
        
        print("✅ AI engine modules imported successfully")
        print("✅ Model references in AI engine files should now use gemini-2.5-flash-preview-05-20")
        return True
    except Exception as e:
        print(f"❌ AI engine test failed: {e}")
        return False

def main():
    """Run all model configuration tests"""
    print("🔧 Testing Gemini Model Configuration")
    print("=" * 50)
    
    tests = [
        test_config_models,
        test_enhanced_generator_model,
        test_ai_engine_references
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 50)
    print(f"Model Configuration Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All model configurations are correct!")
        print("✅ ComplianceGPT is now using gemini-2.5-flash-preview-05-20")
        return True
    else:
        print("⚠️  Some model configuration issues found.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
