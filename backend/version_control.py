
"""
Version Control System for ComplianceGPT
"""

from datetime import datetime

class VersionControlSystem:
    """
    System for managing policy versions and change tracking
    """
    
    def __init__(self, db):
        self.db = db
        self.versions = {}
    
    async def get_policy_versions(self, policy_id):
        """
        Get all versions of a policy
        """
        if policy_id not in self.versions:
            self.versions[policy_id] = [{
                "version": 1,
                "created_at": "2025-05-28T10:00:00Z",
                "created_by": "system",
                "status": "approved",
                "approved_at": "2025-05-28T10:00:00Z",
                "approved_by": "system",
                "changes_summary": "Initial version"
            }]
        
        return {
            "policy_id": policy_id,
            "versions": self.versions[policy_id]
        }
    
    async def create_policy_version(self, policy_id, content, title, changes_summary, user_id):
        """
        Create a new version of a policy
        """
        if policy_id not in self.versions:
            await self.get_policy_versions(policy_id)
        
        new_version = len(self.versions[policy_id]) + 1
        
        version_data = {
            "version": new_version,
            "created_at": datetime.utcnow().isoformat() + "Z",
            "created_by": user_id,
            "status": "pending",
            "changes_summary": changes_summary
        }
        
        self.versions[policy_id].append(version_data)
        
        return {
            "policy_id": policy_id,
            "version": version_data
        }
    
    async def approve_policy_version(self, policy_id, version, approver_id, approval_notes):
        """
        Approve a policy version
        """
        if policy_id not in self.versions:
            raise ValueError(f"Policy {policy_id} not found")
        
        version_index = next((i for i, v in enumerate(self.versions[policy_id]) if v["version"] == version), None)
        
        if version_index is None:
            raise ValueError(f"Version {version} of policy {policy_id} not found")
        
        self.versions[policy_id][version_index]["status"] = "approved"
        self.versions[policy_id][version_index]["approved_at"] = datetime.utcnow().isoformat() + "Z"
        self.versions[policy_id][version_index]["approved_by"] = approver_id
        self.versions[policy_id][version_index]["approval_notes"] = approval_notes
        
        return {
            "policy_id": policy_id,
            "version": version,
            "status": "approved",
            "approved_at": self.versions[policy_id][version_index]["approved_at"],
            "approved_by": approver_id
        }
    
    async def compare_policy_versions(self, policy_id, version1, version2):
        """
        Compare two versions of a policy
        """
        if policy_id not in self.versions:
            raise ValueError(f"Policy {policy_id} not found")
        
        return {
            "policy_id": policy_id,
            "version1": version1,
            "version2": version2,
            "differences": [
                {
                    "type": "addition",
                    "section": "Data Processing",
                    "content": "Added new section on data processing"
                },
                {
                    "type": "deletion",
                    "section": "Data Retention",
                    "content": "Removed outdated retention policy"
                },
                {
                    "type": "modification",
                    "section": "Data Subject Rights",
                    "content": "Updated rights according to new regulations"
                }
            ]
        }
    
    async def rollback_policy(self, policy_id, target_version, user_id, rollback_reason):
        """
        Rollback a policy to a previous version
        """
        if policy_id not in self.versions:
            raise ValueError(f"Policy {policy_id} not found")
        
        version_index = next((i for i, v in enumerate(self.versions[policy_id]) if v["version"] == target_version), None)
        
        if version_index is None:
            raise ValueError(f"Version {target_version} of policy {policy_id} not found")
        
        new_version = len(self.versions[policy_id]) + 1
        
        version_data = {
            "version": new_version,
            "created_at": datetime.utcnow().isoformat() + "Z",
            "created_by": user_id,
            "status": "approved",
            "approved_at": datetime.utcnow().isoformat() + "Z",
            "approved_by": user_id,
            "changes_summary": f"Rollback to version {target_version}: {rollback_reason}",
            "rollback_from": new_version - 1,
            "rollback_to": target_version
        }
        
        self.versions[policy_id].append(version_data)
        
        return {
            "policy_id": policy_id,
            "version": version_data,
            "rollback": {
                "from_version": new_version - 1,
                "to_version": target_version,
                "reason": rollback_reason
            }
        }
