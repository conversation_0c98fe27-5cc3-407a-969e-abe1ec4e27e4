"""
Base Integration Class for ComplianceGPT Evidence Collection
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid
import json
import asyncio
import aiohttp
from pydantic import BaseModel

class IntegrationStatus(str, Enum):
    """Integration connection status"""
    DISCONNECTED = "disconnected"
    CONNECTED = "connected"
    ERROR = "error"
    SYNCING = "syncing"

class EvidenceType(str, Enum):
    """Types of compliance evidence"""
    USER_ACCESS = "user_access"
    ADMIN_LOGS = "admin_logs"
    SECURITY_SETTINGS = "security_settings"
    DATA_ACCESS = "data_access"
    POLICY_ENFORCEMENT = "policy_enforcement"
    AUDIT_TRAIL = "audit_trail"
    INFRASTRUCTURE = "infrastructure"
    CODE_SECURITY = "code_security"

class Evidence(BaseModel):
    """Evidence data model"""
    id: str
    integration: str
    evidence_type: EvidenceType
    title: str
    description: str
    data: Dict[str, Any]
    collected_at: datetime
    compliance_frameworks: List[str]
    control_mappings: Dict[str, str]
    freshness_days: int = 30
    expires_at: Optional[datetime] = None

class IntegrationConfig(BaseModel):
    """Integration configuration model"""
    id: str
    name: str
    provider: str
    credentials: Dict[str, Any]
    settings: Dict[str, Any]
    status: IntegrationStatus
    last_sync: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

class BaseIntegration(ABC):
    """Base class for all compliance integrations"""
    
    def __init__(self, config: IntegrationConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Provider name for this integration"""
        pass
    
    @property 
    @abstractmethod
    def required_scopes(self) -> List[str]:
        """Required OAuth scopes for this integration"""
        pass
    
    @property
    @abstractmethod
    def evidence_types(self) -> List[EvidenceType]:
        """Types of evidence this integration can collect"""
        pass
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def authenticate(self) -> bool:
        """Authenticate with the provider"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """Test the integration connection"""
        pass
    
    @abstractmethod
    async def collect_evidence(self, evidence_type: EvidenceType) -> List[Evidence]:
        """Collect specific type of evidence"""
        pass
    
    async def collect_all_evidence(self) -> List[Evidence]:
        """Collect all available evidence types"""
        all_evidence = []
        for evidence_type in self.evidence_types:
            try:
                evidence = await self.collect_evidence(evidence_type)
                all_evidence.extend(evidence)
            except Exception as e:
                print(f"Error collecting {evidence_type} from {self.provider_name}: {e}")
        return all_evidence
    
    def map_to_frameworks(self, evidence_type: EvidenceType) -> Dict[str, str]:
        """Map evidence to compliance framework controls"""
        mappings = {
            EvidenceType.USER_ACCESS: {
                "gdpr": "Article 32 - Security of processing",
                "soc2": "CC6.1 - Logical access controls",
                "iso27001": "A.9 - Access control"
            },
            EvidenceType.ADMIN_LOGS: {
                "gdpr": "Article 30 - Records of processing",
                "soc2": "CC7.2 - System monitoring",
                "iso27001": "A.12.4 - Logging and monitoring"
            },
            EvidenceType.SECURITY_SETTINGS: {
                "gdpr": "Article 25 - Data protection by design",
                "soc2": "CC1.1 - Security policies",
                "iso27001": "A.5.1 - Information security policy"
            },
            EvidenceType.DATA_ACCESS: {
                "gdpr": "Article 15 - Right of access",
                "soc2": "CC6.3 - Data access controls", 
                "iso27001": "A.9.2 - User access management"
            },
            EvidenceType.POLICY_ENFORCEMENT: {
                "gdpr": "Article 5 - Principles of processing",
                "soc2": "CC2.1 - Policy implementation",
                "iso27001": "A.5.2 - Information security policy"
            },
            EvidenceType.AUDIT_TRAIL: {
                "gdpr": "Article 30 - Records of processing",
                "soc2": "CC7.1 - System monitoring",
                "iso27001": "A.16.1 - Incident management"
            },
            EvidenceType.INFRASTRUCTURE: {
                "gdpr": "Article 32 - Security of processing",
                "soc2": "CC8.1 - Change management",
                "iso27001": "A.12 - Operations security"
            },
            EvidenceType.CODE_SECURITY: {
                "gdpr": "Article 25 - Data protection by design",
                "soc2": "CC8.1 - Change management",
                "iso27001": "A.14 - System acquisition"
            }
        }
        return mappings.get(evidence_type, {})
    
    def create_evidence(
        self, 
        evidence_type: EvidenceType,
        title: str,
        description: str, 
        data: Dict[str, Any],
        compliance_frameworks: List[str] = None
    ) -> Evidence:
        """Create an evidence record"""
        if compliance_frameworks is None:
            compliance_frameworks = ["gdpr", "soc2", "iso27001"]
            
        return Evidence(
            id=str(uuid.uuid4()),
            integration=self.provider_name,
            evidence_type=evidence_type,
            title=title,
            description=description,
            data=data,
            collected_at=datetime.utcnow(),
            compliance_frameworks=compliance_frameworks,
            control_mappings=self.map_to_frameworks(evidence_type)
        )