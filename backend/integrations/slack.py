"""
Slack Integration for ComplianceGPT
Collects user provisioning, workspace policies, and communication data for compliance evidence.
"""

from typing import List, Dict, Any
from datetime import datetime, timedelta
from .base import BaseIntegration, Evidence, EvidenceType

class SlackIntegration(BaseIntegration):
    """Slack integration for compliance evidence collection"""
    
    @property
    def provider_name(self) -> str:
        return "slack"
    
    @property
    def required_scopes(self) -> List[str]:
        return [
            "admin",
            "audit:read",
            "users:read",
            "team:read",
            "channels:read"
        ]
    
    @property
    def evidence_types(self) -> List[EvidenceType]:
        return [
            EvidenceType.USER_ACCESS,
            EvidenceType.ADMIN_LOGS,
            EvidenceType.SECURITY_SETTINGS,
            EvidenceType.POLICY_ENFORCEMENT
        ]
    
    async def authenticate(self) -> bool:
        """Authenticate using Slack OAuth"""
        try:
            bot_token = self.config.credentials.get("bot_token")
            user_token = self.config.credentials.get("user_token")
            
            if not bot_token or not user_token:
                return False
            
            return await self.test_connection()
        except Exception as e:
            print(f"Slack authentication error: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test connection to Slack API"""
        try:
            # Mock connection test
            return True
        except Exception as e:
            print(f"Slack connection test failed: {e}")
            return False
    
    async def collect_evidence(self, evidence_type: EvidenceType) -> List[Evidence]:
        """Collect specific evidence type from Slack"""
        evidence_list = []
        
        try:
            if evidence_type == EvidenceType.USER_ACCESS:
                evidence_list.extend(await self._collect_user_management())
            elif evidence_type == EvidenceType.ADMIN_LOGS:
                evidence_list.extend(await self._collect_audit_logs())
            elif evidence_type == EvidenceType.SECURITY_SETTINGS:
                evidence_list.extend(await self._collect_workspace_settings())
            elif evidence_type == EvidenceType.POLICY_ENFORCEMENT:
                evidence_list.extend(await self._collect_policy_compliance())
                
        except Exception as e:
            print(f"Error collecting {evidence_type} from Slack: {e}")
        
        return evidence_list
    
    async def _collect_user_management(self) -> List[Evidence]:
        """Collect Slack user management data"""
        mock_users = [
            {
                "user_id": "U123ABC456",
                "name": "john.doe",
                "real_name": "John Doe",
                "email": "<EMAIL>",
                "status": "active",
                "is_admin": False,
                "is_owner": False,
                "two_factor_type": "app",
                "last_activity": "2025-01-25T11:30:00Z"
            },
            {
                "user_id": "U789XYZ012", 
                "name": "admin.user",
                "real_name": "Admin User",
                "email": "<EMAIL>",
                "status": "active",
                "is_admin": True,
                "is_owner": True,
                "two_factor_type": "app",
                "last_activity": "2025-01-25T09:15:00Z"
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.USER_ACCESS,
            title="Slack User Management Report",
            description="Workspace users, roles, and security status",
            data={
                "total_users": len(mock_users),
                "active_users": len([u for u in mock_users if u["status"] == "active"]),
                "admin_users": len([u for u in mock_users if u["is_admin"]]),
                "2fa_enabled": len([u for u in mock_users if u["two_factor_type"]]),
                "users": mock_users,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_audit_logs(self) -> List[Evidence]:
        """Collect Slack audit logs"""
        mock_audit_logs = [
            {
                "timestamp": "2025-01-25T08:45:00Z",
                "action": "user_created",
                "actor": "<EMAIL>",
                "entity": "<EMAIL>",
                "details": "User added to workspace",
                "ip_address": "*************"
            },
            {
                "timestamp": "2025-01-24T15:20:00Z",
                "action": "channel_created",
                "actor": "<EMAIL>", 
                "entity": "#confidential-project",
                "details": "Private channel created",
                "ip_address": "*************"
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.ADMIN_LOGS,
            title="Slack Audit Trail",
            description="Administrative actions and workspace changes",
            data={
                "log_count": len(mock_audit_logs),
                "date_range": "Last 30 days",
                "activities": mock_audit_logs,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_workspace_settings(self) -> List[Evidence]:
        """Collect Slack workspace security settings"""
        mock_workspace_settings = {
            "authentication": {
                "sso_enabled": True,
                "two_factor_required": True,
                "session_duration": "12_hours"
            },
            "permissions": {
                "message_retention": "unlimited",
                "file_retention": "unlimited",
                "public_channel_management": "admins_only",
                "private_channel_management": "members"
            },
            "external_access": {
                "guest_invites": "admins_only", 
                "external_shared_channels": False,
                "email_domain_restrictions": True
            },
            "compliance": {
                "data_residency": "EU",
                "legal_hold_enabled": True,
                "export_enabled": True
            }
        }
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.SECURITY_SETTINGS,
            title="Slack Workspace Security Configuration",
            description="Workspace security policies and settings",
            data={
                "workspace_settings": mock_workspace_settings,
                "compliance_score": 92,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_policy_compliance(self) -> List[Evidence]:
        """Collect Slack policy enforcement data"""
        mock_policy_data = {
            "retention_policies": {
                "message_retention_enabled": True,
                "default_retention_period": "7_years",
                "channel_specific_retention": True
            },
            "dlp_policies": {
                "sensitive_data_detection": True,
                "blocked_file_types": [".exe", ".zip", ".dmg"],
                "keyword_monitoring": True
            },
            "access_policies": {
                "idle_timeout": "30_minutes",
                "device_management": True,
                "mobile_app_restrictions": True
            }
        }
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.POLICY_ENFORCEMENT,
            title="Slack Policy Enforcement Status",
            description="Data retention, DLP, and access policy enforcement",
            data={
                "policy_data": mock_policy_data,
                "enforcement_status": "Active",
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]