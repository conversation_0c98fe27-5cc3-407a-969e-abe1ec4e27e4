"""
Google Workspace Integration for ComplianceGPT
Collects admin logs, user access data, and Drive permissions for compliance evidence.
"""

import json
from typing import List, Dict, Any
from datetime import datetime, timedelta
import aiohttp
from .base import BaseIntegration, Evidence, EvidenceType, IntegrationStatus

class GoogleWorkspaceIntegration(BaseIntegration):
    """Google Workspace integration for compliance evidence collection"""
    
    @property
    def provider_name(self) -> str:
        return "google_workspace"
    
    @property
    def required_scopes(self) -> List[str]:
        return [
            "https://www.googleapis.com/auth/admin.reports.audit.readonly",
            "https://www.googleapis.com/auth/admin.directory.user.readonly",
            "https://www.googleapis.com/auth/drive.metadata.readonly"
        ]
    
    @property
    def evidence_types(self) -> List[EvidenceType]:
        return [
            EvidenceType.USER_ACCESS,
            EvidenceType.ADMIN_LOGS,
            EvidenceType.DATA_ACCESS,
            EvidenceType.SECURITY_SETTINGS
        ]
    
    async def authenticate(self) -> bool:
        """Authenticate using OAuth2 credentials"""
        try:
            # In production, this would refresh the OAuth token
            # For now, we'll simulate authentication
            access_token = self.config.credentials.get("access_token")
            if not access_token:
                return False
            
            # Test authentication with a simple API call
            return await self.test_connection()
        except Exception as e:
            print(f"Google Workspace authentication error: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test connection to Google Workspace Admin API"""
        try:
            # Mock connection test - in production this would make actual API call
            access_token = self.config.credentials.get("access_token")
            if not access_token:
                return False
            
            # Simulate successful connection
            return True
        except Exception as e:
            print(f"Google Workspace connection test failed: {e}")
            return False
    
    async def collect_evidence(self, evidence_type: EvidenceType) -> List[Evidence]:
        """Collect specific evidence type from Google Workspace"""
        evidence_list = []
        
        try:
            if evidence_type == EvidenceType.USER_ACCESS:
                evidence_list.extend(await self._collect_user_access())
            elif evidence_type == EvidenceType.ADMIN_LOGS:
                evidence_list.extend(await self._collect_admin_logs())
            elif evidence_type == EvidenceType.DATA_ACCESS:
                evidence_list.extend(await self._collect_drive_access())
            elif evidence_type == EvidenceType.SECURITY_SETTINGS:
                evidence_list.extend(await self._collect_security_settings())
                
        except Exception as e:
            print(f"Error collecting {evidence_type} from Google Workspace: {e}")
        
        return evidence_list
    
    async def _collect_user_access(self) -> List[Evidence]:
        """Collect user access reports from Google Admin Console"""
        # Mock data - in production this would call Admin SDK API
        mock_users = [
            {
                "email": "<EMAIL>",
                "status": "active", 
                "last_login": "2025-01-25T10:30:00Z",
                "admin_privileges": False,
                "2fa_enabled": True,
                "suspended": False
            },
            {
                "email": "<EMAIL>",
                "status": "active",
                "last_login": "2025-01-25T09:15:00Z", 
                "admin_privileges": True,
                "2fa_enabled": True,
                "suspended": False
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.USER_ACCESS,
            title="Google Workspace User Access Report",
            description="Active users, admin privileges, and security settings",
            data={
                "total_users": len(mock_users),
                "active_users": len([u for u in mock_users if u["status"] == "active"]),
                "admin_users": len([u for u in mock_users if u["admin_privileges"]]),
                "2fa_enabled_count": len([u for u in mock_users if u["2fa_enabled"]]),
                "users": mock_users,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_admin_logs(self) -> List[Evidence]:
        """Collect admin activity logs"""
        # Mock admin activity data
        mock_admin_activities = [
            {
                "timestamp": "2025-01-25T08:30:00Z",
                "actor": "<EMAIL>",
                "action": "USER_CREATED",
                "target": "<EMAIL>",
                "ip_address": "*************"
            },
            {
                "timestamp": "2025-01-24T14:22:00Z", 
                "actor": "<EMAIL>",
                "action": "GROUP_SETTINGS_CHANGED",
                "target": "<EMAIL>",
                "ip_address": "*************"
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.ADMIN_LOGS,
            title="Google Workspace Admin Activity Logs",
            description="Administrative actions and changes to workspace settings",
            data={
                "activity_count": len(mock_admin_activities),
                "date_range": "Last 30 days",
                "activities": mock_admin_activities,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_drive_access(self) -> List[Evidence]:
        """Collect Google Drive access and sharing data"""
        # Mock Drive sharing data
        mock_drive_data = [
            {
                "file_id": "1ABC123xyz",
                "file_name": "Customer_Database.xlsx",
                "owner": "<EMAIL>",
                "shared_with": ["<EMAIL>", "<EMAIL>"],
                "sharing_type": "internal_only",
                "last_modified": "2025-01-20T16:45:00Z"
            },
            {
                "file_id": "2DEF456abc", 
                "file_name": "Employee_Records.pdf",
                "owner": "<EMAIL>",
                "shared_with": [],
                "sharing_type": "private",
                "last_modified": "2025-01-18T11:20:00Z"
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.DATA_ACCESS,
            title="Google Drive Data Access Report",
            description="File sharing permissions and access controls",
            data={
                "total_files_analyzed": len(mock_drive_data),
                "externally_shared": 0,
                "internally_shared": 1,
                "private_files": 1,
                "files": mock_drive_data,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_security_settings(self) -> List[Evidence]:
        """Collect workspace security settings"""
        # Mock security settings
        mock_security_settings = {
            "two_factor_authentication": {
                "enabled": True,
                "enforcement": "all_users",
                "backup_codes_enabled": True
            },
            "password_policy": {
                "minimum_length": 12,
                "complexity_required": True,
                "password_reuse_prevention": 24
            },
            "session_settings": {
                "session_timeout": "12_hours",
                "concurrent_sessions_allowed": 3
            },
            "api_access": {
                "less_secure_apps": False,
                "oauth_applications": 5
            }
        }
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.SECURITY_SETTINGS,
            title="Google Workspace Security Configuration",
            description="Workspace-wide security policies and settings",
            data={
                "security_settings": mock_security_settings,
                "compliance_score": 85,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]