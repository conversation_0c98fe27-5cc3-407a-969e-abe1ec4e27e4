"""
AWS Integration for ComplianceGPT
Collects infrastructure security, IAM policies, and CloudTrail logs for compliance evidence.
"""

from typing import List, Dict, Any
from datetime import datetime, timedelta
from .base import BaseIntegration, Evidence, EvidenceType

class AWSIntegration(BaseIntegration):
    """AWS integration for compliance evidence collection"""
    
    @property
    def provider_name(self) -> str:
        return "aws"
    
    @property
    def required_scopes(self) -> List[str]:
        return [
            "iam:ListUsers",
            "iam:ListPolicies", 
            "cloudtrail:LookupEvents",
            "ec2:DescribeSecurityGroups",
            "s3:GetBucketPolicy"
        ]
    
    @property
    def evidence_types(self) -> List[EvidenceType]:
        return [
            EvidenceType.USER_ACCESS,
            EvidenceType.INFRASTRUCTURE,
            EvidenceType.ADMIN_LOGS,
            EvidenceType.SECURITY_SETTINGS
        ]
    
    async def authenticate(self) -> bool:
        """Authenticate using AWS credentials"""
        try:
            access_key = self.config.credentials.get("access_key_id")
            secret_key = self.config.credentials.get("secret_access_key")
            
            if not access_key or not secret_key:
                return False
            
            return await self.test_connection()
        except Exception as e:
            print(f"AWS authentication error: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test connection to AWS APIs"""
        try:
            # Mock connection test
            return True
        except Exception as e:
            print(f"AWS connection test failed: {e}")
            return False
    
    async def collect_evidence(self, evidence_type: EvidenceType) -> List[Evidence]:
        """Collect specific evidence type from AWS"""
        evidence_list = []
        
        try:
            if evidence_type == EvidenceType.USER_ACCESS:
                evidence_list.extend(await self._collect_iam_users())
            elif evidence_type == EvidenceType.INFRASTRUCTURE:
                evidence_list.extend(await self._collect_infrastructure_security())
            elif evidence_type == EvidenceType.ADMIN_LOGS:
                evidence_list.extend(await self._collect_cloudtrail_logs())
            elif evidence_type == EvidenceType.SECURITY_SETTINGS:
                evidence_list.extend(await self._collect_security_configuration())
                
        except Exception as e:
            print(f"Error collecting {evidence_type} from AWS: {e}")
        
        return evidence_list
    
    async def _collect_iam_users(self) -> List[Evidence]:
        """Collect AWS IAM user data"""
        mock_iam_users = [
            {
                "username": "john.doe",
                "user_id": "AIDAEXAMPLE123456789",
                "arn": "arn:aws:iam::123456789012:user/john.doe",
                "create_date": "2024-06-15T10:30:00Z",
                "last_activity": "2025-01-25T11:20:00Z",
                "mfa_enabled": True,
                "access_keys": 1,
                "attached_policies": ["ReadOnlyAccess"],
                "groups": ["Developers"]
            },
            {
                "username": "admin.user",
                "user_id": "AIDAADMIN987654321", 
                "arn": "arn:aws:iam::123456789012:user/admin.user",
                "create_date": "2024-01-10T08:00:00Z",
                "last_activity": "2025-01-25T09:45:00Z",
                "mfa_enabled": True,
                "access_keys": 2,
                "attached_policies": ["AdministratorAccess"],
                "groups": ["Administrators"]
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.USER_ACCESS,
            title="AWS IAM Users Report",
            description="IAM users, policies, and access configuration",
            data={
                "total_users": len(mock_iam_users),
                "active_users": len(mock_iam_users),
                "mfa_enabled": len([u for u in mock_iam_users if u["mfa_enabled"]]),
                "admin_users": len([u for u in mock_iam_users if "AdministratorAccess" in u["attached_policies"]]),
                "users": mock_iam_users,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_infrastructure_security(self) -> List[Evidence]:
        """Collect AWS infrastructure security configuration"""
        mock_infrastructure = {
            "security_groups": [
                {
                    "group_id": "sg-0123456789abcdef0",
                    "group_name": "web-servers",
                    "vpc_id": "vpc-12345678",
                    "inbound_rules": [
                        {"protocol": "tcp", "port": 443, "source": "0.0.0.0/0"},
                        {"protocol": "tcp", "port": 80, "source": "0.0.0.0/0"}
                    ],
                    "outbound_rules": [
                        {"protocol": "all", "port": "all", "destination": "0.0.0.0/0"}
                    ]
                },
                {
                    "group_id": "sg-0987654321fedcba0",
                    "group_name": "database-servers",
                    "vpc_id": "vpc-12345678",
                    "inbound_rules": [
                        {"protocol": "tcp", "port": 3306, "source": "sg-0123456789abcdef0"}
                    ],
                    "outbound_rules": []
                }
            ],
            "s3_buckets": [
                {
                    "bucket_name": "company-data-backup",
                    "encryption": "AES256",
                    "versioning": True,
                    "public_access_blocked": True,
                    "logging_enabled": True
                },
                {
                    "bucket_name": "company-web-assets",
                    "encryption": "AES256", 
                    "versioning": False,
                    "public_access_blocked": False,
                    "logging_enabled": True
                }
            ]
        }
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.INFRASTRUCTURE,
            title="AWS Infrastructure Security Report",
            description="Security groups, S3 bucket configurations, and access controls",
            data={
                "security_groups_count": len(mock_infrastructure["security_groups"]),
                "s3_buckets_count": len(mock_infrastructure["s3_buckets"]),
                "encrypted_buckets": len([b for b in mock_infrastructure["s3_buckets"] if b["encryption"]]),
                "infrastructure": mock_infrastructure,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_cloudtrail_logs(self) -> List[Evidence]:
        """Collect AWS CloudTrail audit logs"""
        mock_cloudtrail_events = [
            {
                "event_time": "2025-01-25T10:30:00Z",
                "event_name": "CreateUser",
                "user_name": "admin.user",
                "source_ip": "*************",
                "user_agent": "aws-cli/2.1.39",
                "resources": [{"resourceName": "new.developer"}],
                "response_elements": {"user": {"userName": "new.developer"}}
            },
            {
                "event_time": "2025-01-24T16:45:00Z",
                "event_name": "PutBucketPolicy",
                "user_name": "admin.user",
                "source_ip": "*************",
                "user_agent": "console.aws.amazon.com",
                "resources": [{"resourceName": "company-data-backup"}],
                "response_elements": None
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.ADMIN_LOGS,
            title="AWS CloudTrail Audit Logs",
            description="Administrative actions and API calls across AWS services",
            data={
                "event_count": len(mock_cloudtrail_events),
                "date_range": "Last 30 days",
                "events": mock_cloudtrail_events,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_security_configuration(self) -> List[Evidence]:
        """Collect AWS security configuration and policies"""
        mock_security_config = {
            "account_settings": {
                "cloudtrail_enabled": True,
                "config_enabled": True,
                "guardduty_enabled": True,
                "security_hub_enabled": True
            },
            "iam_policies": {
                "password_policy": {
                    "minimum_length": 14,
                    "require_symbols": True,
                    "require_numbers": True,
                    "require_uppercase": True,
                    "require_lowercase": True,
                    "max_age": 90
                },
                "mfa_required": True,
                "unused_credentials_report": True
            },
            "encryption": {
                "ebs_encryption_by_default": True,
                "s3_default_encryption": True,
                "rds_encryption_enabled": True
            }
        }
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.SECURITY_SETTINGS,
            title="AWS Security Configuration Report",
            description="Account security settings, IAM policies, and encryption configuration",
            data={
                "security_config": mock_security_config,
                "compliance_score": 94,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]