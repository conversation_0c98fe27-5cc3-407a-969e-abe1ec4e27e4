"""
Microsoft 365 Integration for ComplianceGPT
Collects Azure AD logs, user access data, and SharePoint permissions for compliance evidence.
"""

from typing import List, Dict, Any
from datetime import datetime, timedelta
from .base import BaseIntegration, Evidence, EvidenceType

class Microsoft365Integration(BaseIntegration):
    """Microsoft 365 integration for compliance evidence collection"""
    
    @property
    def provider_name(self) -> str:
        return "microsoft365"
    
    @property
    def required_scopes(self) -> List[str]:
        return [
            "https://graph.microsoft.com/AuditLog.Read.All",
            "https://graph.microsoft.com/User.Read.All",
            "https://graph.microsoft.com/Directory.Read.All",
            "https://graph.microsoft.com/Sites.Read.All"
        ]
    
    @property
    def evidence_types(self) -> List[EvidenceType]:
        return [
            EvidenceType.USER_ACCESS,
            EvidenceType.ADMIN_LOGS,
            EvidenceType.SECURITY_SETTINGS,
            EvidenceType.DATA_ACCESS
        ]
    
    async def authenticate(self) -> bool:
        """Authenticate using Microsoft Graph API"""
        try:
            client_id = self.config.credentials.get("client_id")
            client_secret = self.config.credentials.get("client_secret")
            
            if not client_id or not client_secret:
                return False
            
            return await self.test_connection()
        except Exception as e:
            print(f"Microsoft 365 authentication error: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test connection to Microsoft Graph API"""
        try:
            # Mock connection test
            return True
        except Exception as e:
            print(f"Microsoft 365 connection test failed: {e}")
            return False
    
    async def collect_evidence(self, evidence_type: EvidenceType) -> List[Evidence]:
        """Collect specific evidence type from Microsoft 365"""
        evidence_list = []
        
        try:
            if evidence_type == EvidenceType.USER_ACCESS:
                evidence_list.extend(await self._collect_azure_ad_users())
            elif evidence_type == EvidenceType.ADMIN_LOGS:
                evidence_list.extend(await self._collect_audit_logs())
            elif evidence_type == EvidenceType.SECURITY_SETTINGS:
                evidence_list.extend(await self._collect_security_policies())
            elif evidence_type == EvidenceType.DATA_ACCESS:
                evidence_list.extend(await self._collect_sharepoint_access())
                
        except Exception as e:
            print(f"Error collecting {evidence_type} from Microsoft 365: {e}")
        
        return evidence_list
    
    async def _collect_azure_ad_users(self) -> List[Evidence]:
        """Collect Azure AD user information"""
        mock_users = [
            {
                "user_principal_name": "<EMAIL>",
                "display_name": "John Doe",
                "account_enabled": True,
                "last_sign_in": "2025-01-25T09:30:00Z",
                "mfa_registered": True,
                "admin_roles": [],
                "license_assigned": "E3"
            },
            {
                "user_principal_name": "<EMAIL>", 
                "display_name": "System Admin",
                "account_enabled": True,
                "last_sign_in": "2025-01-25T08:15:00Z",
                "mfa_registered": True,
                "admin_roles": ["Global Administrator"],
                "license_assigned": "E5"
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.USER_ACCESS,
            title="Azure AD User Directory Report",
            description="Active Directory users, roles, and security status",
            data={
                "total_users": len(mock_users),
                "enabled_users": len([u for u in mock_users if u["account_enabled"]]),
                "admin_users": len([u for u in mock_users if u["admin_roles"]]),
                "mfa_registered": len([u for u in mock_users if u["mfa_registered"]]),
                "users": mock_users,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_audit_logs(self) -> List[Evidence]:
        """Collect Microsoft 365 audit logs"""
        mock_audit_logs = [
            {
                "timestamp": "2025-01-25T10:15:00Z",
                "user": "<EMAIL>",
                "operation": "UserAdded",
                "result": "Success",
                "client_ip": "*************",
                "object_id": "<EMAIL>"
            },
            {
                "timestamp": "2025-01-24T16:30:00Z",
                "user": "<EMAIL>",
                "operation": "FileAccessed", 
                "result": "Success",
                "client_ip": "*************",
                "object_id": "Confidential_Report.docx"
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.ADMIN_LOGS,
            title="Microsoft 365 Audit Logs",
            description="Administrative actions and user activities",
            data={
                "log_count": len(mock_audit_logs),
                "date_range": "Last 30 days",
                "activities": mock_audit_logs,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_security_policies(self) -> List[Evidence]:
        """Collect Microsoft 365 security policies"""
        mock_security_policies = {
            "conditional_access": {
                "policies_enabled": 3,
                "mfa_required": True,
                "device_compliance_required": True,
                "location_restrictions": True
            },
            "identity_protection": {
                "risk_policies_enabled": True,
                "sign_in_risk_policy": "Block",
                "user_risk_policy": "Require_password_change"
            },
            "data_loss_prevention": {
                "policies_active": 2,
                "sensitive_info_types": ["Credit Card", "SSN", "EU GDPR"],
                "protection_actions": ["Block", "Notify"]
            }
        }
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.SECURITY_SETTINGS,
            title="Microsoft 365 Security Policies", 
            description="Conditional access, identity protection, and DLP policies",
            data={
                "security_policies": mock_security_policies,
                "compliance_score": 88,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_sharepoint_access(self) -> List[Evidence]:
        """Collect SharePoint access and permissions"""
        mock_sharepoint_data = [
            {
                "site_url": "https://company.sharepoint.com/sites/hr",
                "site_title": "HR Documents",
                "permission_level": "Restricted",
                "members": 5,
                "external_sharing": "Disabled",
                "last_activity": "2025-01-20T14:30:00Z"
            },
            {
                "site_url": "https://company.sharepoint.com/sites/public",
                "site_title": "Public Resources", 
                "permission_level": "All Company",
                "members": 50,
                "external_sharing": "Disabled",
                "last_activity": "2025-01-25T11:45:00Z"
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.DATA_ACCESS,
            title="SharePoint Access Control Report",
            description="SharePoint site permissions and sharing settings",
            data={
                "total_sites": len(mock_sharepoint_data),
                "restricted_sites": 1,
                "external_sharing_enabled": 0,
                "sites": mock_sharepoint_data,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]