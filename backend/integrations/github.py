"""
GitHub Integration for ComplianceGPT
Collects code security, access controls, and development process evidence for compliance.
"""

from typing import List, Dict, Any
from datetime import datetime, timedelta
from .base import BaseIntegration, Evidence, EvidenceType

class GitHubIntegration(BaseIntegration):
    """GitHub integration for compliance evidence collection"""
    
    @property
    def provider_name(self) -> str:
        return "github"
    
    @property
    def required_scopes(self) -> List[str]:
        return [
            "repo",
            "admin:org",
            "read:audit_log",
            "read:user"
        ]
    
    @property
    def evidence_types(self) -> List[EvidenceType]:
        return [
            EvidenceType.USER_ACCESS,
            EvidenceType.CODE_SECURITY,
            EvidenceType.ADMIN_LOGS,
            EvidenceType.SECURITY_SETTINGS
        ]
    
    async def authenticate(self) -> bool:
        """Authenticate using GitHub token"""
        try:
            access_token = self.config.credentials.get("access_token")
            
            if not access_token:
                return False
            
            return await self.test_connection()
        except Exception as e:
            print(f"GitHub authentication error: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test connection to GitHub API"""
        try:
            # Mock connection test
            return True
        except Exception as e:
            print(f"GitHub connection test failed: {e}")
            return False
    
    async def collect_evidence(self, evidence_type: EvidenceType) -> List[Evidence]:
        """Collect specific evidence type from GitHub"""
        evidence_list = []
        
        try:
            if evidence_type == EvidenceType.USER_ACCESS:
                evidence_list.extend(await self._collect_organization_members())
            elif evidence_type == EvidenceType.CODE_SECURITY:
                evidence_list.extend(await self._collect_security_analysis())
            elif evidence_type == EvidenceType.ADMIN_LOGS:
                evidence_list.extend(await self._collect_audit_logs())
            elif evidence_type == EvidenceType.SECURITY_SETTINGS:
                evidence_list.extend(await self._collect_security_policies())
                
        except Exception as e:
            print(f"Error collecting {evidence_type} from GitHub: {e}")
        
        return evidence_list
    
    async def _collect_organization_members(self) -> List[Evidence]:
        """Collect GitHub organization member data"""
        mock_members = [
            {
                "username": "john-doe",
                "email": "<EMAIL>",
                "role": "member",
                "two_factor_enabled": True,
                "last_activity": "2025-01-25T10:15:00Z",
                "repositories_access": 15,
                "admin_privileges": False
            },
            {
                "username": "admin-user",
                "email": "<EMAIL>",
                "role": "owner",
                "two_factor_enabled": True,
                "last_activity": "2025-01-25T08:30:00Z",
                "repositories_access": 25,
                "admin_privileges": True
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.USER_ACCESS,
            title="GitHub Organization Members Report",
            description="Organization members, roles, and access permissions",
            data={
                "total_members": len(mock_members),
                "active_members": len(mock_members),
                "admin_members": len([m for m in mock_members if m["admin_privileges"]]),
                "2fa_enabled": len([m for m in mock_members if m["two_factor_enabled"]]),
                "members": mock_members,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_security_analysis(self) -> List[Evidence]:
        """Collect GitHub security analysis data"""
        mock_security_data = {
            "repositories": [
                {
                    "name": "main-application",
                    "private": True,
                    "branch_protection": True,
                    "required_reviews": 2,
                    "security_alerts": 0,
                    "vulnerability_alerts": True,
                    "code_scanning": True,
                    "secret_scanning": True,
                    "last_scan": "2025-01-25T06:00:00Z"
                },
                {
                    "name": "internal-tools", 
                    "private": True,
                    "branch_protection": True,
                    "required_reviews": 1,
                    "security_alerts": 2,
                    "vulnerability_alerts": True,
                    "code_scanning": False,
                    "secret_scanning": True,
                    "last_scan": "2025-01-24T18:00:00Z"
                }
            ],
            "organization_security": {
                "dependency_security_updates": True,
                "security_advisories": True,
                "vulnerability_reporting": True
            }
        }
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.CODE_SECURITY,
            title="GitHub Security Analysis Report",
            description="Repository security settings, vulnerabilities, and code scanning",
            data={
                "total_repositories": len(mock_security_data["repositories"]),
                "private_repositories": len([r for r in mock_security_data["repositories"] if r["private"]]),
                "protected_branches": len([r for r in mock_security_data["repositories"] if r["branch_protection"]]),
                "security_scanning_enabled": len([r for r in mock_security_data["repositories"] if r["code_scanning"]]),
                "security_data": mock_security_data,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_audit_logs(self) -> List[Evidence]:
        """Collect GitHub audit logs"""
        mock_audit_logs = [
            {
                "timestamp": "2025-01-25T09:20:00Z",
                "action": "repo.create",
                "actor": "admin-user",
                "repository": "new-project",
                "visibility": "private",
                "ip_address": "*************"
            },
            {
                "timestamp": "2025-01-24T14:35:00Z",
                "action": "org.add_member",
                "actor": "admin-user",
                "target_user": "new-developer",
                "role": "member", 
                "ip_address": "*************"
            }
        ]
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.ADMIN_LOGS,
            title="GitHub Organization Audit Logs",
            description="Administrative actions and repository changes",
            data={
                "log_count": len(mock_audit_logs),
                "date_range": "Last 30 days",
                "activities": mock_audit_logs,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]
    
    async def _collect_security_policies(self) -> List[Evidence]:
        """Collect GitHub organization security policies"""
        mock_security_policies = {
            "organization_policies": {
                "two_factor_authentication": "required",
                "member_visibility": "private",
                "repository_creation": "members",
                "repository_deletion": "owners_only"
            },
            "branch_protection": {
                "default_branch_protection": True,
                "required_status_checks": True,
                "required_reviews": True,
                "dismiss_stale_reviews": True,
                "require_code_owner_reviews": True
            },
            "security_features": {
                "dependency_security_updates": True,
                "vulnerability_alerts": True,
                "secret_scanning": True,
                "code_scanning_default": True
            }
        }
        
        evidence = self.create_evidence(
            evidence_type=EvidenceType.SECURITY_SETTINGS,
            title="GitHub Security Policies Configuration",
            description="Organization security policies and repository protection settings",
            data={
                "security_policies": mock_security_policies,
                "compliance_score": 95,
                "collected_at": datetime.utcnow().isoformat()
            }
        )
        
        return [evidence]