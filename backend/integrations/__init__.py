"""
ComplianceGPT Integrations Module
Automated evidence collection from business tools for compliance automation.
"""

from .base import BaseIntegration, IntegrationStatus, EvidenceType
from .google_workspace import GoogleWorkspaceIntegration
from .microsoft365 import Microsoft365Integration
from .slack import SlackIntegration
from .github import GitHubIntegration
from .aws import AWSIntegration

# Integration registry
AVAILABLE_INTEGRATIONS = {
    'google_workspace': GoogleWorkspaceIntegration,
    'microsoft365': Microsoft365Integration, 
    'slack': SlackIntegration,
    'github': GitHubIntegration,
    'aws': AWSIntegration
}

__all__ = [
    'BaseIntegration',
    'IntegrationStatus', 
    'EvidenceType',
    'GoogleWorkspaceIntegration',
    'Microsoft365Integration',
    'SlackIntegration', 
    'GitHubIntegration',
    'AWSIntegration',
    'AVAILABLE_INTEGRATIONS'
]