"""
AI-Powered Risk Assessment for ComplianceGPT
Uses Gemini 2.5 Flash to analyze compliance risks and provide intelligent recommendations
"""

import google.generativeai as genai
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import asyncio
from ..config import GEMINI_API_KEY, GEMINI_CONFIG, MODELS

class AIRiskAssessor:
    """AI-powered compliance risk assessment and analysis"""
    
    def __init__(self):
        genai.configure(api_key=GEMINI_API_KEY)
        
    async def assess_company_risk_profile(
        self,
        company_data: Dict[str, Any],
        frameworks: List[str],
        evidence_data: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Perform comprehensive AI risk assessment"""
        
        prompt = f"""
You are an expert compliance risk assessor. Analyze the following company profile and provide a comprehensive risk assessment for compliance frameworks: {', '.join(frameworks)}.

Company Profile:
- Name: {company_data.get('name', 'Unknown')}
- Industry: {company_data.get('industry', 'Unknown')}
- Employee Count: {company_data.get('employee_count', 'Unknown')}
- Company Type: {company_data.get('company_type', 'Unknown')}
- Data Types Processed: {', '.join(company_data.get('data_types', []))}
- Geographic Locations: {', '.join(company_data.get('locations', ['UK']))}
- Annual Revenue: {company_data.get('revenue', 'Not specified')}

Evidence Available: {len(evidence_data) if evidence_data else 0} evidence items collected

Please provide a detailed risk assessment including:

1. **Overall Risk Score** (0-100, where 100 is highest risk)

2. **Framework-Specific Risk Analysis**:
   - GDPR Compliance Risk (if applicable)
   - SOC 2 Compliance Risk (if applicable) 
   - ISO 27001 Compliance Risk (if applicable)

3. **Industry-Specific Risks**:
   - Regulatory requirements specific to {company_data.get('industry', 'this industry')}
   - Common compliance challenges in this sector
   - Emerging regulatory trends

4. **Data Protection Risk Factors**:
   - Data types processed and associated risks
   - Cross-border data transfer risks
   - Data retention and deletion risks

5. **Technical Security Risks**:
   - Infrastructure security concerns
   - Access control vulnerabilities
   - Monitoring and logging gaps

6. **Operational Risk Factors**:
   - Staff training and awareness gaps
   - Process and procedure weaknesses  
   - Vendor and third-party risks

7. **Prioritized Recommendations**:
   - Top 5 immediate actions needed
   - Medium-term compliance improvements
   - Long-term strategic recommendations

8. **Cost-Benefit Analysis**:
   - Estimated cost of compliance implementation
   - Risk of non-compliance (potential fines, reputation damage)
   - ROI of compliance investment

Format your response as a comprehensive JSON object with clear sections and actionable insights.
"""

        try:
            model = genai.GenerativeModel(
                model_name=MODELS["thinking"],  # Use thinking mode for complex analysis
                generation_config=GEMINI_CONFIG
            )
            
            response = await asyncio.to_thread(model.generate_content, prompt)
            
            # Parse AI response
            risk_assessment = self._parse_risk_assessment(response.text)
            
            # Add metadata
            risk_assessment.update({
                'assessment_date': datetime.utcnow().isoformat(),
                'company_profile': company_data,
                'frameworks_assessed': frameworks,
                'evidence_items_analyzed': len(evidence_data) if evidence_data else 0,
                'ai_model': 'gemini-2.5-flash-preview-05-20',
                'assessment_id': f"risk_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            })
            
            return risk_assessment
            
        except Exception as e:
            return {
                'error': f"Risk assessment failed: {str(e)}",
                'assessment_date': datetime.utcnow().isoformat(),
                'overall_risk_score': 'Unknown'
            }
    
    async def analyze_evidence_gaps(
        self,
        framework: str,
        evidence_data: List[Dict[str, Any]],
        required_controls: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """AI analysis of evidence gaps and compliance readiness"""
        
        prompt = f"""
You are a compliance auditor analyzing evidence collection for {framework.upper()} compliance.

Required Controls: {json.dumps(required_controls, indent=2)}

Collected Evidence: {json.dumps(evidence_data, indent=2)}

Please provide a detailed analysis including:

1. **Evidence Coverage Score** (0-100%)
2. **Critical Gaps Identified**
3. **Evidence Quality Assessment** 
4. **Audit Readiness Level**
5. **Specific Recommendations** for each gap
6. **Timeline to Full Compliance**
7. **Risk Level** for each missing control

Format as JSON with clear actionable insights.
"""

        try:
            model = genai.GenerativeModel(
                model_name=MODELS["basic"],
                generation_config=GEMINI_CONFIG
            )
            
            response = await asyncio.to_thread(model.generate_content, prompt)
            
            return {
                'framework': framework,
                'analysis': response.text,
                'analyzed_at': datetime.utcnow().isoformat(),
                'evidence_count': len(evidence_data),
                'controls_count': len(required_controls)
            }
            
        except Exception as e:
            return {
                'error': f"Evidence gap analysis failed: {str(e)}",
                'framework': framework
            }
    
    async def generate_compliance_roadmap(
        self,
        risk_assessment: Dict[str, Any],
        target_frameworks: List[str],
        timeline_months: int = 12
    ) -> Dict[str, Any]:
        """Generate AI-powered compliance implementation roadmap"""
        
        prompt = f"""
Create a detailed compliance implementation roadmap based on this risk assessment:

Risk Assessment: {json.dumps(risk_assessment, indent=2)}

Target Frameworks: {', '.join(target_frameworks)}
Implementation Timeline: {timeline_months} months

Generate a month-by-month roadmap including:

1. **Phase Breakdown** (Planning, Implementation, Testing, Certification)
2. **Monthly Milestones** with specific deliverables
3. **Resource Requirements** (people, tools, budget)
4. **Risk Mitigation Steps** for each phase
5. **Success Metrics** and KPIs
6. **Certification Preparation** timeline
7. **Continuous Monitoring** setup

Format as a detailed JSON roadmap with clear timelines and responsibilities.
"""

        try:
            model = genai.GenerativeModel(
                model_name=MODELS["thinking"],
                generation_config=GEMINI_CONFIG
            )
            
            response = await asyncio.to_thread(model.generate_content, prompt)
            
            return {
                'roadmap': response.text,
                'target_frameworks': target_frameworks,
                'timeline_months': timeline_months,
                'generated_at': datetime.utcnow().isoformat(),
                'based_on_assessment': risk_assessment.get('assessment_id', 'unknown')
            }
            
        except Exception as e:
            return {
                'error': f"Roadmap generation failed: {str(e)}",
                'target_frameworks': target_frameworks
            }
    
    def _parse_risk_assessment(self, ai_response: str) -> Dict[str, Any]:
        """Parse and structure AI risk assessment response"""
        try:
            # Try to extract JSON from the response
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = ai_response[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # Fallback: structure the text response
                return {
                    'overall_risk_score': self._extract_risk_score(ai_response),
                    'assessment_summary': ai_response,
                    'structured_analysis': 'AI response parsing needed',
                    'recommendations': self._extract_recommendations(ai_response)
                }
        except:
            return {
                'overall_risk_score': 'Unknown',
                'assessment_summary': ai_response,
                'parsing_error': 'Could not structure AI response'
            }
    
    def _extract_risk_score(self, text: str) -> str:
        """Extract risk score from AI response"""
        import re
        
        # Look for patterns like "Risk Score: 75" or "75/100"
        patterns = [
            r'risk score[:\s]+(\d+)',
            r'score[:\s]+(\d+)',
            r'(\d+)/100',
            r'(\d+)%'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return 'Unknown'
    
    def _extract_recommendations(self, text: str) -> List[str]:
        """Extract recommendations from AI response"""
        recommendations = []
        
        # Look for numbered lists or bullet points
        import re
        
        # Find lines that start with numbers or bullets
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if re.match(r'^\d+\.', line) or line.startswith('•') or line.startswith('-'):
                recommendations.append(line)
        
        return recommendations[:10]  # Limit to top 10 recommendations