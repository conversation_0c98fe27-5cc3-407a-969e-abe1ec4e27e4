"""
AI Compliance Advisor for ComplianceGPT
Intelligent compliance guidance and recommendations using Gemini 2.5 Flash
"""

import google.generativeai as genai
from typing import Dict, List, Any, Optional
from datetime import datetime
import asyncio
from ..config import GEMINI_API_KEY, GEMINI_CONFIG, MODELS

class ComplianceAdvisor:
    """AI-powered compliance advisor providing intelligent guidance"""
    
    def __init__(self):
        genai.configure(api_key=GEMINI_API_KEY)
    
    async def get_framework_recommendation(
        self,
        company_profile: Dict[str, Any],
        business_goals: List[str] = None
    ) -> Dict[str, Any]:
        """AI recommendation for optimal compliance frameworks"""
        
        prompt = f"""
You are a senior compliance consultant advising a UK SMB on which compliance frameworks to prioritize.

Company Profile:
- Industry: {company_profile.get('industry', 'Unknown')}
- Employee Count: {company_profile.get('employee_count', 'Unknown')}
- Annual Revenue: {company_profile.get('revenue', 'Not specified')}
- Data Types: {', '.join(company_profile.get('data_types', []))}
- Target Markets: {', '.join(company_profile.get('markets', ['UK']))}
- Customer Types: {company_profile.get('customer_types', 'Unknown')}

Business Goals: {', '.join(business_goals) if business_goals else 'Not specified'}

Available frameworks: GDPR, SOC 2 Type II, ISO 27001:2022

Provide recommendations including:

1. **Primary Framework Recommendation** with detailed justification
2. **Implementation Priority Order** (1st, 2nd, 3rd)
3. **Business Impact Analysis** for each framework
4. **Cost-Benefit Analysis** including potential revenue impact
5. **Timeline Recommendations** for each framework
6. **Industry-Specific Considerations**
7. **Customer/Market Requirements**
8. **Risk vs Reward Assessment**

Format as JSON with clear reasoning for each recommendation.
"""

        try:
            model = genai.GenerativeModel(
                model_name=MODELS["thinking"],
                generation_config=GEMINI_CONFIG
            )
            
            response = await asyncio.to_thread(model.generate_content, prompt)
            
            return {
                'recommendations': response.text,
                'company_profile': company_profile,
                'generated_at': datetime.utcnow().isoformat(),
                'advisor_version': 'gemini-2.5-flash-preview-05-20'
            }
            
        except Exception as e:
            return {
                'error': f"Framework recommendation failed: {str(e)}",
                'company_profile': company_profile
            }
    
    async def analyze_audit_readiness(
        self,
        framework: str,
        policies: List[Dict[str, Any]],
        evidence: List[Dict[str, Any]],
        controls_coverage: Dict[str, Any]
    ) -> Dict[str, Any]:
        """AI analysis of audit readiness and preparation recommendations"""
        
        prompt = f"""
You are a compliance auditor assessing {framework.upper()} audit readiness.

Current Status:
- Policies in place: {len(policies)}
- Evidence collected: {len(evidence)}
- Controls coverage: {controls_coverage}

Policy Details: {str(policies)[:2000]}...
Evidence Summary: {str(evidence)[:2000]}...

Provide detailed audit readiness assessment:

1. **Audit Readiness Score** (0-100%)
2. **Strengths Identified**
3. **Critical Gaps** that could fail an audit
4. **Documentation Quality** assessment
5. **Evidence Sufficiency** analysis
6. **Pre-Audit Checklist** with specific actions
7. **Timeline to Audit Ready** status
8. **Estimated Audit Pass Probability**
9. **Auditor Questions** preparation
10. **Post-Audit Recommendations**

Focus on specific, actionable advice for {framework.upper()} certification.
"""

        try:
            model = genai.GenerativeModel(
                model_name=MODELS["thinking"],
                generation_config=GEMINI_CONFIG
            )
            
            response = await asyncio.to_thread(model.generate_content, prompt)
            
            return {
                'framework': framework,
                'audit_readiness': response.text,
                'policies_analyzed': len(policies),
                'evidence_analyzed': len(evidence),
                'assessment_date': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'error': f"Audit readiness analysis failed: {str(e)}",
                'framework': framework
            }
    
    async def generate_training_recommendations(
        self,
        company_profile: Dict[str, Any],
        frameworks: List[str],
        current_knowledge_gaps: List[str] = None
    ) -> Dict[str, Any]:
        """AI-generated employee training recommendations"""
        
        prompt = f"""
Design a comprehensive compliance training program for this organization:

Company: {company_profile.get('name', 'Unknown')}
Industry: {company_profile.get('industry', 'Unknown')}
Employee Count: {company_profile.get('employee_count', 'Unknown')}
Frameworks: {', '.join(frameworks)}

Current Knowledge Gaps: {', '.join(current_knowledge_gaps) if current_knowledge_gaps else 'Assessment needed'}

Create a detailed training program including:

1. **Role-Based Training Matrix**
   - Executives/Leadership training needs
   - IT/Security team requirements  
   - General employee awareness
   - HR and compliance team training

2. **Framework-Specific Modules**
   - Module breakdown for each framework
   - Learning objectives and outcomes
   - Practical exercises and scenarios

3. **Training Schedule and Timeline**
   - Initial training rollout plan
   - Ongoing education calendar
   - Certification requirements

4. **Assessment and Measurement**
   - Knowledge testing approach
   - Compliance behavior monitoring
   - Training effectiveness metrics

5. **Resource Requirements**
   - Internal vs external training needs
   - Budget estimates
   - Technology requirements

6. **Industry-Specific Scenarios**
   - Real-world examples for {company_profile.get('industry', 'this industry')}
   - Common compliance challenges
   - Best practices implementation

Format as a comprehensive training program with clear implementation steps.
"""

        try:
            model = genai.GenerativeModel(
                model_name=MODELS["basic"],
                generation_config=GEMINI_CONFIG
            )
            
            response = await asyncio.to_thread(model.generate_content, prompt)
            
            return {
                'training_program': response.text,
                'target_frameworks': frameworks,
                'company_profile': company_profile,
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'error': f"Training recommendations failed: {str(e)}",
                'frameworks': frameworks
            }
    
    async def provide_regulatory_updates(
        self,
        frameworks: List[str],
        industry: str,
        region: str = 'UK'
    ) -> Dict[str, Any]:
        """AI analysis of latest regulatory changes and their impact"""
        
        prompt = f"""
Provide the latest regulatory updates and changes relevant to:

Frameworks: {', '.join(frameworks)}
Industry: {industry}
Region: {region}

Based on your knowledge cutoff, analyze:

1. **Recent Regulatory Changes** (2024-2025)
   - New requirements or amendments
   - Updated guidance documents
   - Enforcement trends

2. **Industry-Specific Developments**
   - Sector-specific regulations for {industry}
   - Emerging compliance requirements
   - Industry best practices evolution

3. **Regional Considerations**
   - {region}-specific regulatory landscape
   - Brexit impact on compliance (if applicable)
   - Cross-border data transfer updates

4. **Implementation Impact**
   - Changes needed to current practices
   - Timeline for compliance with new requirements
   - Cost implications

5. **Future Outlook**
   - Anticipated regulatory developments
   - Emerging trends to monitor
   - Preparation recommendations

6. **Action Items**
   - Immediate compliance actions needed
   - Policy updates required
   - Training program adjustments

Provide specific, actionable guidance with clear timelines where applicable.
"""

        try:
            model = genai.GenerativeModel(
                model_name=MODELS["thinking"],
                generation_config=GEMINI_CONFIG
            )
            
            response = await asyncio.to_thread(model.generate_content, prompt)
            
            return {
                'regulatory_updates': response.text,
                'frameworks': frameworks,
                'industry': industry,
                'region': region,
                'analysis_date': datetime.utcnow().isoformat(),
                'note': 'Based on AI knowledge cutoff - verify with current regulatory sources'
            }
            
        except Exception as e:
            return {
                'error': f"Regulatory updates analysis failed: {str(e)}",
                'frameworks': frameworks
            }
    
    async def generate_incident_response_guidance(
        self,
        incident_type: str,
        frameworks: List[str],
        company_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """AI guidance for compliance incident response"""
        
        prompt = f"""
Provide detailed incident response guidance for a compliance-related incident:

Incident Type: {incident_type}
Affected Frameworks: {', '.join(frameworks)}
Company: {company_data.get('industry', 'Unknown')} industry, {company_data.get('employee_count', 'Unknown')} employees

Generate comprehensive incident response plan including:

1. **Immediate Actions** (first 1-4 hours)
   - Critical steps to take immediately
   - Key stakeholders to notify
   - Initial damage assessment

2. **Investigation Phase** (24-72 hours)
   - Evidence collection procedures
   - Root cause analysis approach
   - Documentation requirements

3. **Notification Requirements**
   - Regulatory notification obligations
   - Customer/stakeholder communications
   - Timeline requirements

4. **Remediation Steps**
   - Technical remediation actions
   - Process improvements needed
   - Control enhancements

5. **Communication Plan**
   - Internal communication strategy
   - External stakeholder management
   - Media response (if needed)

6. **Compliance Implications**
   - Framework-specific requirements
   - Potential penalties or sanctions
   - Certification impact assessment

7. **Lessons Learned Integration**
   - Policy updates needed
   - Training program adjustments
   - Monitoring improvements

Provide specific, actionable guidance with clear timelines and responsibilities.
"""

        try:
            model = genai.GenerativeModel(
                model_name=MODELS["thinking"],
                generation_config=GEMINI_CONFIG
            )
            
            response = await asyncio.to_thread(model.generate_content, prompt)
            
            return {
                'incident_response_plan': response.text,
                'incident_type': incident_type,
                'frameworks': frameworks,
                'company_profile': company_data,
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'error': f"Incident response guidance failed: {str(e)}",
                'incident_type': incident_type
            }