"""
Enhanced Policy Routes - API endpoints for enterprise-grade policy generation
"""

import traceback
from fastapi import APIRouter, HTTPException, Header, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.responses import Response, JSONResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import json
import asyncio
import logging
from datetime import datetime

from ..services.enhanced_policy_generator import (
    EnhancedPolicyGenerator, 
    PolicyRequest, 
    AIService,
    GenerationStage
)

from ..auth_utils import get_user_from_token
from ..database import db # For shared DB instance

# Removed local get_user_from_token function


logger = logging.getLogger(__name__)

router = APIRouter()

class EnhancedPolicyGenerationRequest(BaseModel):
    """Enhanced policy generation request model"""
    framework: str = Field(..., description="GDPR, SOC2, or ISO27001")
    company_name: str = Field(..., min_length=2, max_length=100)
    company_type: str = Field(..., description="Technology Company, Financial Services, etc.")
    industry: str = Field(..., description="Technology, Financial, Healthcare, etc.")
    employee_count: int = Field(..., ge=1, le=100000)
    data_types: List[str] = Field(..., min_items=1, description="Types of data processed")
    existing_policies: List[str] = Field(default=[], description="Existing policy names")
    complexity_level: str = Field(default="comprehensive", description="comprehensive, advanced, expert")
    jurisdiction: str = Field(default="UK", description="Primary jurisdiction")
    risk_profile: str = Field(default="medium", description="low, medium, high")
    
    # Enhanced customization options
    customization: Optional[Dict[str, Any]] = Field(default={})
    generation_mode: str = Field(default="enhanced", description="enhanced, comprehensive, enterprise")

class PolicyGenerationResponse(BaseModel):
    """Enhanced policy generation response model"""
    policy_id: str
    generation_id: str
    status: str
    metadata: Dict[str, Any]
    quality_metrics: Dict[str, float]
    policy_sections: Dict[str, Dict[str, Any]]
    generation_config: Dict[str, Any]

class GenerationProgressUpdate(BaseModel):
    """Progress update model for WebSocket"""
    stage: str
    progress_percentage: float
    stage_name: str
    estimated_time_remaining: int
    current_word_count: int
    status: str
    message: str

# WebSocket connection manager for real-time progress
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, generation_id: str):
        await websocket.accept()
        self.active_connections[generation_id] = websocket
    
    def disconnect(self, generation_id: str):
        if generation_id in self.active_connections:
            del self.active_connections[generation_id]
    
    async def send_progress_update(self, generation_id: str, update: GenerationProgressUpdate):
        if generation_id in self.active_connections:
            try:
                await self.active_connections[generation_id].send_text(update.model_dump_json())
            except:
                self.disconnect(generation_id)

manager = ConnectionManager()

@router.websocket("/ws/policy-generation/{generation_id}")
async def websocket_policy_generation(websocket: WebSocket, generation_id: str):
    """WebSocket endpoint for real-time policy generation progress"""
    await manager.connect(websocket, generation_id)
    try:
        while True:
            # Keep connection alive
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        manager.disconnect(generation_id)

@router.post("/api/policies/generate-enhanced", response_model=PolicyGenerationResponse)
async def generate_enhanced_policy(
    request: EnhancedPolicyGenerationRequest,
    background_tasks: BackgroundTasks,
    authorization: str = Header(None)
):
    """Generate comprehensive enterprise-grade policy"""
    
    # Authenticate user
    try:
        user = await get_user_from_token(authorization, db=db)
        logger.info(f"Enhanced policy generation request from user: {user['email']}")
    except Exception as e:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    # Validate request
    if request.framework.upper() not in ["GDPR", "SOC2", "ISO27001"]:
        raise HTTPException(status_code=400, detail="Unsupported framework")
    
    if request.generation_mode not in ["enhanced", "comprehensive", "enterprise"]:
        raise HTTPException(status_code=400, detail="Invalid generation mode")
    
    try:
        # Initialize enhanced generator
        # Ensure GEMINI_API_KEY is available, e.g., from a top-level import or config
        # The shared 'db' instance is already imported at the top of this file.
        from ..config import GEMINI_API_KEY # Ensure GEMINI_API_KEY is imported if not already available globally
        
        ai_service = AIService(GEMINI_API_KEY)
        generator = EnhancedPolicyGenerator(db, ai_service) # Corrected order: db first, then ai_service
        
        # Create policy request
        policy_request = PolicyRequest(
            framework=request.framework.upper(),
            company_name=request.company_name,
            company_type=request.company_type,
            industry=request.industry,
            employee_count=request.employee_count,
            data_types=request.data_types,
            existing_policies=request.existing_policies,
            complexity_level=request.complexity_level,
            user_id=user["id"],
            jurisdiction=request.jurisdiction,
            risk_profile=request.risk_profile
        )
        
        # Generate policy with progress tracking
        generation_start = datetime.utcnow()
        
        # Send initial progress update
        await manager.send_progress_update(
            policy_request.user_id,
            GenerationProgressUpdate(
                stage="initializing",
                progress_percentage=0,
                stage_name="Initializing Generation",
                estimated_time_remaining=120,
                current_word_count=0,
                status="starting",
                message="Preparing comprehensive policy generation..."
            )
        )
        
        # Generate comprehensive policy
        policy_document = await generator.generate_comprehensive_policy(policy_request)
        
        generation_end = datetime.utcnow()
        
        # Send completion update
        await manager.send_progress_update(
            policy_request.user_id,
            GenerationProgressUpdate(
                stage="completed",
                progress_percentage=100,
                stage_name="Generation Complete",
                estimated_time_remaining=0,
                current_word_count=policy_document.metadata["total_words"],
                status="completed",
                message=f"Successfully generated {policy_document.metadata['total_words']:,} word comprehensive policy"
            )
        )
        
        # Prepare response
        response = PolicyGenerationResponse(
            policy_id=policy_document.policy_id,
            generation_id=policy_document.policy_id,
            status="completed",
            metadata={
                "total_pages": policy_document.metadata["total_pages"],
                "total_words": policy_document.metadata["total_words"],
                "generation_time": policy_document.metadata["generation_time"],
                "overall_completeness_score": policy_document.quality_metrics.get("overall_score", 0.0),  # Corrected source and key
                "created_at": generation_start.isoformat(),
                "completed_at": generation_end.isoformat()
            },
            quality_metrics=policy_document.quality_metrics,
            policy_sections={
                "framework": {
                    "word_count": policy_document.content["framework"].word_count,
                    "completeness_score": policy_document.content["framework"].completeness_score,
                    "generation_time": policy_document.content["framework"].generation_time
                },
                "procedures": {
                    "word_count": policy_document.content["procedures"].word_count,
                    "completeness_score": policy_document.content["procedures"].completeness_score,
                    "generation_time": policy_document.content["procedures"].generation_time
                },
                "tools": {
                    "word_count": policy_document.content["tools"].word_count,
                    "completeness_score": policy_document.content["tools"].completeness_score,
                    "generation_time": policy_document.content["tools"].generation_time
                }
            },
            generation_config=policy_document.generation_config
        )
        
        logger.info(f"Enhanced policy generated successfully: {policy_document.metadata.get('total_words', 0):,} words, {policy_document.quality_metrics.get('overall_score', 0.0):.2%} quality")  # Corrected source for quality score
        
        return response
        
    except Exception as e:
        tb_str = traceback.format_exc()
        # Log the error with traceback (our custom logger should still try to log this)
        logger.error(f"Enhanced policy generation failed: {str(e)}\nTRACEBACK:\n{tb_str}", exc_info=False) # exc_info=False now as we are manually logging tb_str
        
        # Send error update
        # Ensure user variable is available if used here, or use a known safe ID like policy_request.user_id if defined
        # For now, assuming 'user' is available as the error we are debugging occurs after auth.
        await manager.send_progress_update(
            user["id"],
            GenerationProgressUpdate(
                stage="error",
                progress_percentage=0,
                stage_name="Generation Failed",
                estimated_time_remaining=0,
                current_word_count=0,
                status="failed",
                message=f"Policy generation failed: {str(e)}"
            )
        )
        
        # Return traceback in the HTTP response for debugging
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}\n\nTRACEBACK:\n{tb_str}")

@router.get("/api/policies/{policy_id}/enhanced")
async def get_enhanced_policy(
    policy_id: str,
    section: Optional[str] = None,
    authorization: str = Header(None)
):
    """Get enhanced policy with full content and metadata"""
    
    # Authenticate user
    user = await get_user_from_token(authorization, db=db)
    
    try:
        # Using shared db instance imported from ...server
        
        # Get policy from database
        policy = await db.policies.find_one({
            "id": policy_id,
            "user_id": user["id"]
        })
        
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")
        
        # Remove MongoDB ObjectId
        if "_id" in policy:
            del policy["_id"]
        
        # Return specific section or full policy
        if section and section in ["framework", "procedures", "tools", "assembled"]:
            if "content_sections" in policy and section in policy["content_sections"]:
                return {
                    "policy_id": policy_id,
                    "section": section,
                    "content": policy["content_sections"][section],
                    "metadata": policy.get("metadata", {}),
                    "quality_metrics": policy.get("quality_metrics", {})
                }
            else:
                raise HTTPException(status_code=404, detail=f"Section {section} not found")
        
        # Return full policy
        return policy
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving enhanced policy: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve policy")

@router.get("/api/policies/{policy_id}/export/comprehensive")
async def export_comprehensive_policy(
    policy_id: str,
    format: str = "markdown",  # markdown, html, pdf, docx
    authorization: str = Header(None)
):
    """Export comprehensive policy in various formats"""
    
    # Authenticate user
    user = await get_user_from_token(authorization, db=db)
    
    try:
        # Using shared db instance imported from ...server
        
        # Get policy
        policy = await db.policies.find_one({
            "id": policy_id,
            "user_id": user["id"]
        })
        
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")
        
        # Get assembled content
        if "content_sections" in policy and "assembled" in policy["content_sections"]:
            content = policy["content_sections"]["assembled"]["content"]
        else:
            raise HTTPException(status_code=404, detail="Assembled policy content not found")
        
        # Format content based on requested format
        if format == "markdown":
            response_content = content
            media_type = "text/markdown"
            filename = f"{policy['company_name']}_{policy['framework']}_Policy.md"
            
        elif format == "html":
            # Convert markdown to HTML (basic implementation)
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{policy['title']}</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }}
        h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
        h2 {{ color: #34495e; }}
        h3 {{ color: #7f8c8d; }}
        .metadata {{ background: #ecf0f1; padding: 20px; border-radius: 5px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="metadata">
        <strong>Policy Metadata:</strong><br>
        Total Words: {policy.get('metadata', {}).get('total_words', 'N/A'):,}<br>
        Total Pages: {policy.get('metadata', {}).get('total_pages', 'N/A')}<br>
        Quality Score: {policy.get('metadata', {}).get('overall_completeness_score', 0):.1%}<br>
        Generated: {policy.get('created_at', 'N/A')}
    </div>
    <div>{content.replace(chr(10), '<br>')}</div>
</body>
</html>
"""
            response_content = html_content
            media_type = "text/html"
            filename = f"{policy['company_name']}_{policy['framework']}_Policy.html"
            
        else:
            raise HTTPException(status_code=400, detail="Unsupported format. Use: markdown, html")
        
        # Return file response
        return Response(
            content=response_content,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting policy: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to export policy")

@router.get("/api/policies/enhanced/analytics")
async def get_enhanced_analytics(
    authorization: str = Header(None),
    days: int = 30
):
    """Get enhanced policy generation analytics"""
    
    # Authenticate user
    user = await get_user_from_token(authorization, db=db)
    
    try:
        from datetime import timedelta # Keep timedelta import
        # Using shared db instance imported from ...server
        
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get user's enhanced policies
        policies = await db.policies.find({
            "user_id": user["id"],
            "created_at": {"$gte": start_date, "$lte": end_date},
            "content_sections": {"$exists": True}  # Enhanced policies only
        }).to_list(None)
        
        if not policies:
            return {
                "analytics": {
                    "total_policies": 0,
                    "average_quality_score": 0,
                    "average_word_count": 0,
                    "average_generation_time": 0,
                    "framework_breakdown": {},
                    "quality_trends": [],
                    "performance_metrics": {}
                }
            }
        
        # Calculate analytics
        total_policies = len(policies)
        
        # Quality metrics
        quality_scores = [p.get("metadata", {}).get("overall_completeness_score", 0) for p in policies]
        average_quality = sum(quality_scores) / total_policies if quality_scores else 0
        
        # Word count metrics
        word_counts = [p.get("metadata", {}).get("total_words", 0) for p in policies]
        average_words = sum(word_counts) / total_policies if word_counts else 0
        
        # Generation time metrics
        generation_times = [p.get("metadata", {}).get("generation_time", 0) for p in policies]
        average_time = sum(generation_times) / total_policies if generation_times else 0
        
        # Framework breakdown
        framework_breakdown = {}
        for policy in policies:
            framework = policy.get("framework", "Unknown")
            framework_breakdown[framework] = framework_breakdown.get(framework, 0) + 1
        
        # Quality trends (daily averages)
        daily_quality = {}
        for policy in policies:
            date_str = policy.get("created_at", datetime.utcnow()).strftime("%Y-%m-%d")
            if date_str not in daily_quality:
                daily_quality[date_str] = []
            daily_quality[date_str].append(policy.get("metadata", {}).get("overall_completeness_score", 0))
        
        quality_trends = [
            {
                "date": date,
                "average_quality": sum(scores) / len(scores)
            }
            for date, scores in sorted(daily_quality.items())
        ]
        
        # Performance metrics
        performance_metrics = {
            "policies_meeting_target": sum(1 for score in quality_scores if score >= 0.90),
            "policies_over_20_pages": sum(1 for p in policies if p.get("metadata", {}).get("total_pages", 0) >= 20),
            "policies_under_120s": sum(1 for time in generation_times if time <= 120),
            "average_pages": sum(p.get("metadata", {}).get("total_pages", 0) for p in policies) / total_policies if policies else 0
        }
        
        return {
            "analytics": {
                "date_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                    "days": days
                },
                "total_policies": total_policies,
                "average_quality_score": round(average_quality, 3),
                "average_word_count": round(average_words),
                "average_generation_time": round(average_time, 1),
                "framework_breakdown": framework_breakdown,
                "quality_trends": quality_trends,
                "performance_metrics": performance_metrics
            }
        }
        
    except Exception as e:
        logger.error(f"Error generating analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate analytics")

@router.delete("/api/policies/{policy_id}/enhanced")
async def delete_enhanced_policy(
    policy_id: str,
    authorization: str = Header(None)
):
    """Delete enhanced policy and all associated data"""
    
    # Authenticate user
    user = await get_user_from_token(authorization, db=db)
    
    try:
        # Using shared db instance imported from ...server
        
        # Delete policy
        result = await db.policies.delete_one({
            "id": policy_id,
            "user_id": user["id"]
        })
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Policy not found")
        
        # Delete associated generation logs
        await db.generation_logs.delete_many({
            "generation_id": policy_id
        })
        
        logger.info(f"Enhanced policy deleted: {policy_id}")
        
        return {"message": "Policy deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting policy: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete policy")
