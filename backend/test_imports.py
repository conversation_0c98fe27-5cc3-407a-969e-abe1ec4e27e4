#!/usr/bin/env python3
"""
Simple test script to verify that the enhanced policy generator components work
"""

import sys
import os

def test_basic_imports():
    """Test basic Python imports"""
    try:
        import asyncio
        import json
        import uuid
        print("✅ Basic Python imports successful")
        return True
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        return False

def test_policy_models():
    """Test policy models import"""
    try:
        from services.policy_models import PolicyRequest, GenerationStage, StageOutput
        print("✅ Policy models imported successfully")
        
        # Test creating a PolicyRequest
        request = PolicyRequest(
            framework="gdpr",
            company_name="Test Corp",
            company_type="SaaS",
            industry="Technology",
            employee_count=50,
            data_types=["Personal Data"],
            existing_policies=[],
            complexity_level="standard",
            user_id="test-user"
        )
        print(f"✅ PolicyRequest created: {request.company_name}")
        return True
    except Exception as e:
        print(f"❌ Policy models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quality_validator():
    """Test policy quality validator"""
    try:
        from services.policy_quality_validator import PolicyQualityValidator
        validator = PolicyQualityValidator()
        
        # Test validation
        test_content = "This is a test policy with risk assessment and data protection measures."
        score = validator.validate_framework(test_content)
        print(f"✅ PolicyQualityValidator works, test score: {score}")
        return True
    except Exception as e:
        print(f"❌ Quality validator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_manager():
    """Test prompt manager (simplified)"""
    try:
        # Test if prompt files exist
        prompt_dir = os.path.join("prompts", "templates")
        required_files = [
            "framework_prompt.txt",
            "procedures_prompt.txt", 
            "tools_prompt.txt",
            "assembly_prompt.txt"
        ]
        
        for filename in required_files:
            filepath = os.path.join(prompt_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, 'r') as f:
                    content = f.read()
                    print(f"✅ {filename} exists ({len(content)} chars)")
            else:
                print(f"❌ {filename} missing")
                return False
        
        print("✅ All prompt templates found")
        return True
    except Exception as e:
        print(f"❌ Prompt manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Enhanced Policy Generator Components")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_policy_models,
        test_quality_validator,
        test_prompt_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced Policy Generator is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
