"""
Evidence Collection API for ComplianceGPT
Manages automated evidence collection from integrated business tools.
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import uuid
import asyncio

from .integrations import AVAILABLE_INTEGRATIONS
from .integrations.base import IntegrationConfig, IntegrationStatus, Evidence, EvidenceType
from .models import ComplianceFrameworkType
from .auth_utils import get_current_user_id

router = APIRouter()

# In-memory storage for demo - in production this would be MongoDB
evidence_store: Dict[str, Dict[str, Evidence]] = {}  # Keyed by user_id, then evidence_id
integration_configs: Dict[str, Dict[str, IntegrationConfig]] = {}  # Keyed by user_id, then integration_id

class EvidenceRequest(BaseModel):
    """Request model for evidence collection"""
    integration_id: str
    evidence_types: Optional[List[EvidenceType]] = None
    framework: Optional[ComplianceFrameworkType] = None

class IntegrationSetupRequest(BaseModel):
    """Request model for setting up integrations"""
    provider: str
    name: str
    credentials: Dict[str, Any]
    settings: Dict[str, Any] = {}

@router.get("/integrations")
async def get_available_integrations():
    """Get list of available integrations"""
    integrations = []
    for provider, integration_class in AVAILABLE_INTEGRATIONS.items():
        # Create temporary instance to get metadata
        temp_config = IntegrationConfig(
            id="temp",
            name="temp",
            provider=provider,
            credentials={},
            settings={},
            status=IntegrationStatus.DISCONNECTED,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        temp_integration = integration_class(temp_config)
        
        integrations.append({
            "provider": provider,
            "name": temp_integration.provider_name.title(),
            "required_scopes": temp_integration.required_scopes,
            "evidence_types": temp_integration.evidence_types,
            "status": "available"
        })
    
    return {"integrations": integrations}

@router.get("/integrations/configured")
async def get_configured_integrations(user_id: str = Depends(get_current_user_id)):
    """Get list of configured integrations"""
    configured = []
    user_specific_configs = integration_configs.get(user_id, {})
    for config in user_specific_configs.values():
        # Convert to JSON-serializable format
        config_dict = {
            "id": config.id,
            "name": config.name,
            "provider": config.provider,
            "status": config.status.value,
            "last_sync": config.last_sync.isoformat() if config.last_sync else None,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat()
        }
        configured.append(config_dict)
    
    return {"configured_integrations": configured}

@router.post("/integrations/setup")
async def setup_integration(request: IntegrationSetupRequest, user_id: str = Depends(get_current_user_id)):
    """Set up a new integration"""
    try:
        if request.provider not in AVAILABLE_INTEGRATIONS:
            raise HTTPException(status_code=400, detail=f"Unknown provider: {request.provider}")
        
        integration_id = str(uuid.uuid4())
        config = IntegrationConfig(
            id=integration_id,
            name=request.name,
            provider=request.provider,
            credentials=request.credentials,
            settings=request.settings,
            status=IntegrationStatus.DISCONNECTED,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Try to connect and authenticate
        integration_class = AVAILABLE_INTEGRATIONS[request.provider]
        async with integration_class(config) as integration:
            if await integration.authenticate():
                config.status = IntegrationStatus.CONNECTED
                config.last_sync = datetime.utcnow()
            else:
                config.status = IntegrationStatus.ERROR
        
        if user_id not in integration_configs:
            integration_configs[user_id] = {}
        integration_configs[user_id][integration_id] = config
        
        return {
            "integration_id": integration_id,
            "status": config.status.value,
            "message": "Integration setup completed"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to setup integration: {str(e)}")

@router.post("/integrations/{integration_id}/test")
async def test_integration(integration_id: str, user_id: str = Depends(get_current_user_id)):
    """Test an integration connection"""
    user_configs = integration_configs.get(user_id, {})
    if integration_id not in user_configs:
        raise HTTPException(status_code=404, detail="Integration not found")
    
    config = user_configs[integration_id]
    integration_class = AVAILABLE_INTEGRATIONS[config.provider]
    
    try:
        async with integration_class(config) as integration:
            success = await integration.test_connection()
            status = IntegrationStatus.CONNECTED if success else IntegrationStatus.ERROR
            
            # Update config
            config.status = status
            config.updated_at = datetime.utcnow()
            
            return {
                "integration_id": integration_id,
                "test_result": success,
                "status": status.value
            }
    except Exception as e:
        config.status = IntegrationStatus.ERROR
        config.updated_at = datetime.utcnow()
        raise HTTPException(status_code=500, detail=f"Connection test failed: {str(e)}")

@router.post("/evidence/collect")
async def collect_evidence(request: EvidenceRequest, background_tasks: BackgroundTasks, user_id: str = Depends(get_current_user_id)):
    """Collect evidence from specified integration"""
    user_integration_configs = integration_configs.get(user_id, {})
    if request.integration_id not in user_integration_configs:
        raise HTTPException(status_code=404, detail="Integration not found for this user")
    
    config = user_integration_configs[request.integration_id]
    
    if config.status != IntegrationStatus.CONNECTED:
        raise HTTPException(status_code=400, detail="Integration not connected")
    
    # Start evidence collection in background
    background_tasks.add_task(
        _collect_evidence_background,
        user_id,  # Pass user_id to the background task
        config,
        request.evidence_types,
        request.framework
    )
    
    return {
        "message": "Evidence collection started",
        "integration_id": request.integration_id,
        "status": "processing"
    }

async def _collect_evidence_background(
    user_id: str,  # Added user_id parameter
    config: IntegrationConfig,
    evidence_types: Optional[List[EvidenceType]] = None,
    framework: Optional[ComplianceFrameworkType] = None
):
    """Background task for evidence collection"""
    try:
        integration_class = AVAILABLE_INTEGRATIONS[config.provider]
        async with integration_class(config) as integration:
            
            config.status = IntegrationStatus.SYNCING
            config.updated_at = datetime.utcnow()
            
            if evidence_types:
                # Collect specific evidence types
                all_evidence = []
                for evidence_type in evidence_types:
                    evidence = await integration.collect_evidence(evidence_type)
                    all_evidence.extend(evidence)
            else:
                # Collect all evidence
                all_evidence = await integration.collect_all_evidence()
            
            # Filter by framework if specified
            if framework:
                filtered_evidence = [
                    e for e in all_evidence 
                    if framework.value in e.compliance_frameworks
                ]
                all_evidence = filtered_evidence
            
            # Store evidence
            for evidence_obj in all_evidence:
                if user_id not in evidence_store:
                    evidence_store[user_id] = {}
                evidence_store[user_id][evidence_obj.id] = evidence_obj
            
            # Update integration status
            config.status = IntegrationStatus.CONNECTED
            config.last_sync = datetime.utcnow()
            config.updated_at = datetime.utcnow()
            
            print(f"✅ Collected {len(all_evidence)} evidence items from {config.provider}")
            
    except Exception as e:
        print(f"Background evidence collection failed: {e}")
        config.status = IntegrationStatus.ERROR
        config.updated_at = datetime.utcnow()

@router.get("/evidence")
async def get_evidence(
    integration: Optional[str] = None,
    evidence_type: Optional[EvidenceType] = None,
    framework: Optional[ComplianceFrameworkType] = None,
    limit: int = 50
):
    """Get collected evidence with optional filters"""
    evidence_list = list(evidence_store.values())
    
    # Apply filters
    if integration:
        evidence_list = [e for e in evidence_list if e.integration == integration]
    
    if evidence_type:
        evidence_list = [e for e in evidence_list if e.evidence_type == evidence_type]
    
    if framework:
        evidence_list = [e for e in evidence_list if framework.value in e.compliance_frameworks]
    
    # Sort by collection date (newest first)
    evidence_list.sort(key=lambda x: x.collected_at, reverse=True)
    
    # Limit results
    evidence_list = evidence_list[:limit]
    
    # Convert to JSON-serializable format
    serialized_evidence = []
    for evidence in evidence_list:
        evidence_dict = {
            "id": evidence.id,
            "integration": evidence.integration,
            "evidence_type": evidence.evidence_type.value,
            "title": evidence.title,
            "description": evidence.description,
            "data": evidence.data,
            "collected_at": evidence.collected_at.isoformat(),
            "compliance_frameworks": evidence.compliance_frameworks,
            "control_mappings": evidence.control_mappings
        }
        serialized_evidence.append(evidence_dict)
    
    return {
        "evidence": serialized_evidence,
        "total_count": len(evidence_list),
        "filters_applied": {
            "integration": integration,
            "evidence_type": evidence_type.value if evidence_type else None,
            "framework": framework.value if framework else None
        }
    }

@router.get("/evidence/{evidence_id}")
async def get_evidence_details(evidence_id: str, user_id: str = Depends(get_current_user_id)):
    """Get detailed evidence information"""
    user_evidence = evidence_store.get(user_id, {})
    if evidence_id not in user_evidence:
        raise HTTPException(status_code=404, detail="Evidence not found for this user")
    
    evidence = user_evidence[evidence_id]
    
    return {
        "id": evidence.id,
        "integration": evidence.integration,
        "evidence_type": evidence.evidence_type.value,
        "title": evidence.title,
        "description": evidence.description,
        "data": evidence.data,
        "collected_at": evidence.collected_at.isoformat(),
        "compliance_frameworks": evidence.compliance_frameworks,
        "control_mappings": evidence.control_mappings,
        "freshness_days": evidence.freshness_days,
        "expires_at": evidence.expires_at.isoformat() if evidence.expires_at else None
    }

@router.get("/evidence/analysis/gaps")
async def analyze_evidence_gaps(framework: ComplianceFrameworkType):
    """Analyze evidence gaps for a specific compliance framework"""
    framework_evidence = [
        e for e in evidence_store.values()
        if framework.value in e.compliance_frameworks
    ]
    
    # Group evidence by type
    evidence_by_type = {}
    for evidence in framework_evidence:
        evidence_type = evidence.evidence_type.value
        if evidence_type not in evidence_by_type:
            evidence_by_type[evidence_type] = []
        evidence_by_type[evidence_type].append(evidence)
    
    # Define required evidence types per framework
    required_evidence = {
        "gdpr": [
            EvidenceType.USER_ACCESS.value,
            EvidenceType.DATA_ACCESS.value,
            EvidenceType.ADMIN_LOGS.value,
            EvidenceType.SECURITY_SETTINGS.value,
            EvidenceType.POLICY_ENFORCEMENT.value
        ],
        "soc2": [
            EvidenceType.USER_ACCESS.value,
            EvidenceType.ADMIN_LOGS.value,
            EvidenceType.SECURITY_SETTINGS.value,
            EvidenceType.INFRASTRUCTURE.value,
            EvidenceType.CODE_SECURITY.value
        ],
        "iso27001": [
            EvidenceType.USER_ACCESS.value,
            EvidenceType.ADMIN_LOGS.value,
            EvidenceType.SECURITY_SETTINGS.value,
            EvidenceType.INFRASTRUCTURE.value,
            EvidenceType.AUDIT_TRAIL.value
        ]
    }
    
    framework_requirements = required_evidence.get(framework.value, [])
    missing_evidence = [
        req for req in framework_requirements
        if req not in evidence_by_type
    ]
    
    # Calculate freshness
    stale_evidence = []
    for evidence in framework_evidence:
        days_old = (datetime.utcnow() - evidence.collected_at).days
        if days_old > evidence.freshness_days:
            stale_evidence.append({
                "id": evidence.id,
                "title": evidence.title,
                "days_old": days_old,
                "freshness_limit": evidence.freshness_days
            })
    
    return {
        "framework": framework.value,
        "evidence_summary": {
            "total_evidence": len(framework_evidence),
            "evidence_types_covered": len(evidence_by_type),
            "required_types": len(framework_requirements),
            "coverage_percentage": round((len(evidence_by_type) / len(framework_requirements)) * 100, 1) if framework_requirements else 0
        },
        "gaps": {
            "missing_evidence_types": missing_evidence,
            "stale_evidence": stale_evidence
        },
        "recommendations": [
            f"Set up integration for {evidence_type}" for evidence_type in missing_evidence
        ] + [
            f"Refresh {evidence['title']}" for evidence in stale_evidence
        ]
    }

@router.delete("/integrations/{integration_id}")
async def remove_integration(integration_id: str, user_id: str = Depends(get_current_user_id)):
    """Remove an integration configuration"""
    user_configs = integration_configs.get(user_id, {})
    if integration_id not in user_configs:
        raise HTTPException(status_code=404, detail="Integration not found")
    
    # Remove related evidence
    user_evidence_store = evidence_store.get(user_id, {})
    related_evidence_ids = []
    if user_evidence_store: # Check if the user has any evidence stored
        related_evidence_ids = [
            ev_id for ev_id, ev in user_evidence_store.items()
            if ev.integration_id == integration_id # Check against evidence.integration_id
        ]
        
        for ev_id_to_delete in related_evidence_ids:
            del user_evidence_store[ev_id_to_delete]
        
        if not user_evidence_store: # If user's evidence store is now empty, remove user's key
            del evidence_store[user_id]
    
    # Remove integration config
    del user_configs[integration_id]
    if not user_configs: # If the user has no more integrations, remove the user_id key
        del integration_configs[user_id]
    
    return {
        "message": "Integration removed successfully",
        "removed_evidence_count": len(related_evidence_ids)
    }