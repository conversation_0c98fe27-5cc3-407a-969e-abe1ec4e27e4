"""
Circuit breaker implementation for Enhanced Policy Generator.

This module provides circuit breaker functionality to protect external services
from cascading failures and provide graceful degradation during outages.
"""

import asyncio
import time
from enum import Enum
from typing import (
    Any, Callable, Optional, Union, Type, Tuple, List,
    Awaitable, TypeVar, Dict, NamedTuple
)
from dataclasses import dataclass, field
from functools import wraps
import logging
from contextlib import asynccontextmanager, contextmanager

from backend.exceptions.enhanced_policy_exceptions import (
    EnhancedPolicyException,
    AIServiceException,
    AIRateLimitException,
    TimeoutException
)

logger = logging.getLogger(__name__)

T = TypeVar('T')


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation, requests pass through
    OPEN = "open"          # Circuit is open, requests fail fast
    HALF_OPEN = "half_open"  # Testing if service has recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker behavior."""
    failure_threshold: int = 5  # Number of failures before opening
    recovery_timeout: float = 60.0  # Seconds before trying half-open
    expected_exception: Tuple[Type[Exception], ...] = (
        AIServiceException,
        AIRateLimitException,
        ConnectionError,
        TimeoutError,
        OSError,
    )
    success_threshold: int = 3  # Consecutive successes in half-open to close
    timeout_seconds: Optional[float] = 30.0  # Request timeout
    
    # Monitoring and metrics
    max_requests_half_open: int = 3  # Max requests to allow in half-open state
    reset_timeout: float = 300.0  # Time to reset failure count (5 minutes)


class CircuitBreakerStats(NamedTuple):
    """Statistics for circuit breaker monitoring."""
    state: CircuitBreakerState
    failure_count: int
    success_count: int
    last_failure_time: Optional[float]
    last_success_time: Optional[float]
    total_requests: int
    total_failures: int
    total_successes: int
    state_changes: int
    uptime_seconds: float


class CircuitBreakerOpenException(EnhancedPolicyException):
    """Exception raised when circuit breaker is open."""
    
    def __init__(
        self,
        service_name: str,
        failure_count: int,
        last_failure: Optional[str] = None,
        recovery_time: Optional[float] = None,
        correlation_id: Optional[str] = None
    ):
        self.service_name = service_name
        self.failure_count = failure_count
        self.last_failure = last_failure
        self.recovery_time = recovery_time
        
        message = f"Circuit breaker open for {service_name} (failures: {failure_count})"
        if recovery_time:
            message += f", recovery in {recovery_time:.1f}s"
        
        super().__init__(
            message=message,
            error_code="CIRCUIT_BREAKER_OPEN",
            correlation_id=correlation_id,
            context={
                "service_name": service_name,
                "failure_count": failure_count,
                "last_failure": last_failure,
                "recovery_time": recovery_time,
            }
        )


class CircuitBreaker:
    """
    Circuit breaker implementation with state management.
    
    Protects external services from cascading failures by monitoring
    failure rates and providing fail-fast behavior when services are down.
    """
    
    def __init__(
        self, 
        name: str, 
        config: Optional[CircuitBreakerConfig] = None
    ):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        
        # State management
        self._state = CircuitBreakerState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time: Optional[float] = None
        self._last_success_time: Optional[float] = None
        self._last_state_change_time = time.time()
        self._half_open_request_count = 0
        
        # Statistics
        self._total_requests = 0
        self._total_failures = 0
        self._total_successes = 0
        self._state_changes = 0
        self._creation_time = time.time()
        
        # Synchronization
        self._lock = asyncio.Lock()
    
    @property
    def state(self) -> CircuitBreakerState:
        """Get current circuit breaker state."""
        return self._state
    
    @property
    def is_closed(self) -> bool:
        """Check if circuit breaker is closed (normal operation)."""
        return self._state == CircuitBreakerState.CLOSED
    
    @property
    def is_open(self) -> bool:
        """Check if circuit breaker is open (failing fast)."""
        return self._state == CircuitBreakerState.OPEN
    
    @property
    def is_half_open(self) -> bool:
        """Check if circuit breaker is half open (testing recovery)."""
        return self._state == CircuitBreakerState.HALF_OPEN
    
    def get_stats(self) -> CircuitBreakerStats:
        """Get current circuit breaker statistics."""
        return CircuitBreakerStats(
            state=self._state,
            failure_count=self._failure_count,
            success_count=self._success_count,
            last_failure_time=self._last_failure_time,
            last_success_time=self._last_success_time,
            total_requests=self._total_requests,
            total_failures=self._total_failures,
            total_successes=self._total_successes,
            state_changes=self._state_changes,
            uptime_seconds=time.time() - self._creation_time
        )
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt state reset."""
        if self._state != CircuitBreakerState.OPEN:
            return False
        
        if self._last_failure_time is None:
            return True
        
        return time.time() - self._last_failure_time >= self.config.recovery_timeout
    
    def _should_reset_failure_count(self) -> bool:
        """Check if failure count should be reset due to time passage."""
        if self._last_failure_time is None:
            return False
        
        return time.time() - self._last_failure_time >= self.config.reset_timeout
    
    async def _transition_to_state(
        self, 
        new_state: CircuitBreakerState,
        correlation_id: Optional[str] = None
    ):
        """Transition to a new circuit breaker state."""
        if new_state == self._state:
            return
        
        old_state = self._state
        self._state = new_state
        self._last_state_change_time = time.time()
        self._state_changes += 1
        
        # Reset counters on state transitions
        if new_state == CircuitBreakerState.HALF_OPEN:
            self._half_open_request_count = 0
            self._success_count = 0
        elif new_state == CircuitBreakerState.CLOSED:
            self._failure_count = 0
            self._success_count = 0
            self._half_open_request_count = 0
        
        logger.info(
            f"Circuit breaker '{self.name}' transitioned from {old_state.value} to {new_state.value}",
            extra={
                "correlation_id": correlation_id,
                "circuit_breaker": self.name,
                "old_state": old_state.value,
                "new_state": new_state.value,
                "failure_count": self._failure_count,
                "success_count": self._success_count,
            }
        )
    
    async def _handle_success(self, correlation_id: Optional[str] = None):
        """Handle successful operation."""
        async with self._lock:
            self._success_count += 1
            self._total_successes += 1
            self._last_success_time = time.time()
            
            # Reset failure count on success after reset timeout
            if self._should_reset_failure_count():
                self._failure_count = 0
            
            # State transitions based on success
            if self._state == CircuitBreakerState.HALF_OPEN:
                if self._success_count >= self.config.success_threshold:
                    await self._transition_to_state(CircuitBreakerState.CLOSED, correlation_id)
            
            logger.debug(
                f"Circuit breaker '{self.name}' recorded success",
                extra={
                    "correlation_id": correlation_id,
                    "circuit_breaker": self.name,
                    "success_count": self._success_count,
                    "state": self._state.value,
                }
            )
    
    async def _handle_failure(
        self, 
        exception: Exception,
        correlation_id: Optional[str] = None
    ):
        """Handle failed operation."""
        async with self._lock:
            self._failure_count += 1
            self._total_failures += 1
            self._last_failure_time = time.time()
            
            logger.warning(
                f"Circuit breaker '{self.name}' recorded failure: {str(exception)}",
                extra={
                    "correlation_id": correlation_id,
                    "circuit_breaker": self.name,
                    "failure_count": self._failure_count,
                    "state": self._state.value,
                    "exception": str(exception),
                }
            )
            
            # State transitions based on failure
            if self._state == CircuitBreakerState.CLOSED:
                if self._failure_count >= self.config.failure_threshold:
                    await self._transition_to_state(CircuitBreakerState.OPEN, correlation_id)
            
            elif self._state == CircuitBreakerState.HALF_OPEN:
                # Any failure in half-open goes back to open
                await self._transition_to_state(CircuitBreakerState.OPEN, correlation_id)
    
    async def _check_state_transition(self, correlation_id: Optional[str] = None):
        """Check if state should transition based on time and conditions."""
        async with self._lock:
            if self._state == CircuitBreakerState.OPEN and self._should_attempt_reset():
                await self._transition_to_state(CircuitBreakerState.HALF_OPEN, correlation_id)
    
    def _can_execute(self) -> bool:
        """Check if request can be executed based on current state."""
        if self._state == CircuitBreakerState.CLOSED:
            return True
        
        if self._state == CircuitBreakerState.OPEN:
            return False
        
        if self._state == CircuitBreakerState.HALF_OPEN:
            return self._half_open_request_count < self.config.max_requests_half_open
        
        return False
    
    async def call_async(
        self,
        func: Callable[..., Awaitable[T]],
        *args,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> T:
        """
        Execute an async function with circuit breaker protection.
        
        Args:
            func: The async function to execute
            *args: Positional arguments for the function
            correlation_id: Optional correlation ID for logging
            **kwargs: Keyword arguments for the function
            
        Returns:
            The result of the function call
            
        Raises:
            CircuitBreakerOpenException: If circuit breaker is open
            The original exception: If the function fails
        """
        await self._check_state_transition(correlation_id)
        
        # Check if we can execute
        if not self._can_execute():
            recovery_time = None
            if self._last_failure_time and self._state == CircuitBreakerState.OPEN:
                elapsed = time.time() - self._last_failure_time
                recovery_time = max(0, self.config.recovery_timeout - elapsed)
            
            raise CircuitBreakerOpenException(
                service_name=self.name,
                failure_count=self._failure_count,
                last_failure=str(self._last_failure_time) if self._last_failure_time else None,
                recovery_time=recovery_time,
                correlation_id=correlation_id
            )
        
        # Track request attempt
        self._total_requests += 1
        if self._state == CircuitBreakerState.HALF_OPEN:
            self._half_open_request_count += 1
        
        start_time = time.time()
        
        try:
            logger.debug(
                f"Circuit breaker '{self.name}' executing request",
                extra={
                    "correlation_id": correlation_id,
                    "circuit_breaker": self.name,
                    "state": self._state.value,
                    "function": func.__name__,
                }
            )
            
            # Execute with timeout if configured
            if self.config.timeout_seconds:
                result = await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=self.config.timeout_seconds
                )
            else:
                result = await func(*args, **kwargs)
            
            await self._handle_success(correlation_id)
            return result
        
        except asyncio.TimeoutError as e:
            timeout_exception = TimeoutException(
                message=f"Circuit breaker '{self.name}' operation timed out",
                operation=func.__name__,
                timeout_seconds=self.config.timeout_seconds,
                elapsed_seconds=time.time() - start_time,
                correlation_id=correlation_id
            )
            await self._handle_failure(timeout_exception, correlation_id)
            raise timeout_exception
        
        except Exception as e:
            # Only handle configured exception types as failures
            if isinstance(e, self.config.expected_exception):
                await self._handle_failure(e, correlation_id)
            else:
                # For unexpected exceptions, still log but don't count as circuit breaker failure
                logger.error(
                    f"Circuit breaker '{self.name}' encountered unexpected exception: {str(e)}",
                    extra={
                        "correlation_id": correlation_id,
                        "circuit_breaker": self.name,
                        "exception": str(e),
                        "exception_type": type(e).__name__,
                    }
                )
            raise
    
    def call_sync(
        self,
        func: Callable[..., T],
        *args,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> T:
        """
        Execute a sync function with circuit breaker protection.
        
        Args:
            func: The function to execute
            *args: Positional arguments for the function
            correlation_id: Optional correlation ID for logging
            **kwargs: Keyword arguments for the function
            
        Returns:
            The result of the function call
            
        Raises:
            CircuitBreakerOpenException: If circuit breaker is open
            The original exception: If the function fails
        """
        # For sync operations, we need to run state checks
        # This is a simplified version - in production you might want
        # to use threading.Lock instead of asyncio.Lock
        
        # Simple state check without async lock
        current_time = time.time()
        if (self._state == CircuitBreakerState.OPEN and 
            self._last_failure_time and
            current_time - self._last_failure_time >= self.config.recovery_timeout):
            self._state = CircuitBreakerState.HALF_OPEN
            self._half_open_request_count = 0
            self._success_count = 0
        
        # Check if we can execute
        if not self._can_execute():
            recovery_time = None
            if self._last_failure_time and self._state == CircuitBreakerState.OPEN:
                elapsed = current_time - self._last_failure_time
                recovery_time = max(0, self.config.recovery_timeout - elapsed)
            
            raise CircuitBreakerOpenException(
                service_name=self.name,
                failure_count=self._failure_count,
                last_failure=str(self._last_failure_time) if self._last_failure_time else None,
                recovery_time=recovery_time,
                correlation_id=correlation_id
            )
        
        # Track request attempt
        self._total_requests += 1
        if self._state == CircuitBreakerState.HALF_OPEN:
            self._half_open_request_count += 1
        
        try:
            logger.debug(
                f"Circuit breaker '{self.name}' executing sync request",
                extra={
                    "correlation_id": correlation_id,
                    "circuit_breaker": self.name,
                    "state": self._state.value,
                    "function": func.__name__,
                }
            )
            
            result = func(*args, **kwargs)
            
            # Handle success (simplified sync version)
            self._success_count += 1
            self._total_successes += 1
            self._last_success_time = time.time()
            
            if self._should_reset_failure_count():
                self._failure_count = 0
            
            if (self._state == CircuitBreakerState.HALF_OPEN and 
                self._success_count >= self.config.success_threshold):
                self._state = CircuitBreakerState.CLOSED
                self._failure_count = 0
                self._success_count = 0
                self._half_open_request_count = 0
            
            return result
        
        except Exception as e:
            # Handle failure (simplified sync version)
            if isinstance(e, self.config.expected_exception):
                self._failure_count += 1
                self._total_failures += 1
                self._last_failure_time = time.time()
                
                if (self._state == CircuitBreakerState.CLOSED and 
                    self._failure_count >= self.config.failure_threshold):
                    self._state = CircuitBreakerState.OPEN
                elif self._state == CircuitBreakerState.HALF_OPEN:
                    self._state = CircuitBreakerState.OPEN
            
            raise


@asynccontextmanager
async def circuit_breaker_context(
    circuit_breaker: CircuitBreaker,
    correlation_id: Optional[str] = None
):
    """
    Async context manager for circuit breaker protection.
    
    Usage:
        async with circuit_breaker_context(cb, correlation_id) as cb:
            result = await cb.call_async(some_function, arg1, arg2)
    """
    try:
        yield circuit_breaker
    except Exception as e:
        logger.error(
            f"Circuit breaker context error: {str(e)}",
            extra={"correlation_id": correlation_id, "circuit_breaker": circuit_breaker.name}
        )
        raise


def with_circuit_breaker(
    circuit_breaker: CircuitBreaker,
    correlation_id_key: str = "correlation_id"
):
    """
    Decorator for adding circuit breaker protection to async functions.
    
    Args:
        circuit_breaker: The circuit breaker instance to use
        correlation_id_key: Key name for correlation ID in kwargs
    
    Returns:
        Decorated function with circuit breaker protection
    """
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            correlation_id = kwargs.pop(correlation_id_key, None)
            return await circuit_breaker.call_async(
                func, *args, correlation_id=correlation_id, **kwargs
            )
        return wrapper
    return decorator


def with_circuit_breaker_sync(
    circuit_breaker: CircuitBreaker,
    correlation_id_key: str = "correlation_id"
):
    """
    Decorator for adding circuit breaker protection to sync functions.
    
    Args:
        circuit_breaker: The circuit breaker instance to use
        correlation_id_key: Key name for correlation ID in kwargs
    
    Returns:
        Decorated function with circuit breaker protection
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> T:
            correlation_id = kwargs.pop(correlation_id_key, None)
            return circuit_breaker.call_sync(
                func, *args, correlation_id=correlation_id, **kwargs
            )
        return wrapper
    return decorator


# Global circuit breaker registry
_circuit_breakers: Dict[str, CircuitBreaker] = {}


def get_circuit_breaker(
    name: str, 
    config: Optional[CircuitBreakerConfig] = None
) -> CircuitBreaker:
    """
    Get or create a circuit breaker by name.
    
    Args:
        name: Unique name for the circuit breaker
        config: Configuration (only used when creating new circuit breaker)
        
    Returns:
        Circuit breaker instance
    """
    if name not in _circuit_breakers:
        _circuit_breakers[name] = CircuitBreaker(name, config)
    return _circuit_breakers[name]


def get_all_circuit_breakers() -> Dict[str, CircuitBreaker]:
    """Get all registered circuit breakers."""
    return _circuit_breakers.copy()


def reset_circuit_breaker(name: str) -> bool:
    """
    Reset a circuit breaker to closed state.
    
    Args:
        name: Name of the circuit breaker to reset
        
    Returns:
        True if circuit breaker was found and reset
    """
    if name in _circuit_breakers:
        cb = _circuit_breakers[name]
        cb._state = CircuitBreakerState.CLOSED
        cb._failure_count = 0
        cb._success_count = 0
        cb._half_open_request_count = 0
        cb._last_failure_time = None
        logger.info(f"Circuit breaker '{name}' manually reset to closed state")
        return True
    return False


def clear_circuit_breakers():
    """Clear all circuit breakers (useful for testing)."""
    global _circuit_breakers
    _circuit_breakers.clear()


# Predefined circuit breaker configurations
AI_SERVICE_CIRCUIT_BREAKER_CONFIG = CircuitBreakerConfig(
    failure_threshold=3,
    recovery_timeout=30.0,
    success_threshold=2,
    max_requests_half_open=2,
    timeout_seconds=120.0,
    expected_exception=(
        AIServiceException,
        AIRateLimitException,
        ConnectionError,
        TimeoutError,
        OSError,
    ),
    reset_timeout=300.0,  # 5 minutes
)

QUICK_CIRCUIT_BREAKER_CONFIG = CircuitBreakerConfig(
    failure_threshold=2,
    recovery_timeout=10.0,
    success_threshold=1,
    max_requests_half_open=1,
    timeout_seconds=30.0,
    reset_timeout=60.0,  # 1 minute
)

RESILIENT_CIRCUIT_BREAKER_CONFIG = CircuitBreakerConfig(
    failure_threshold=10,
    recovery_timeout=120.0,
    success_threshold=5,
    max_requests_half_open=5,
    timeout_seconds=300.0,
    reset_timeout=600.0,  # 10 minutes
)


def create_ai_service_circuit_breaker(name: str) -> CircuitBreaker:
    """Create a circuit breaker optimized for AI service calls."""
    return CircuitBreaker(name, AI_SERVICE_CIRCUIT_BREAKER_CONFIG)


def create_quick_circuit_breaker(name: str) -> CircuitBreaker:
    """Create a circuit breaker for quick operations."""
    return CircuitBreaker(name, QUICK_CIRCUIT_BREAKER_CONFIG)


def create_resilient_circuit_breaker(name: str) -> CircuitBreaker:
    """Create a circuit breaker for critical operations requiring resilience."""
    return CircuitBreaker(name, RESILIENT_CIRCUIT_BREAKER_CONFIG)