"""
Enhanced logging configuration for the Enhanced Policy Generator.

This module provides structured JSON logging with correlation IDs,
context enrichment, and integration with the existing logging infrastructure.
"""

import logging
import logging.config
import json
import sys
import time
from typing import Dict, Any, Optional, Union, List
from datetime import datetime, timezone
import uuid
from dataclasses import dataclass, field
from contextlib import contextmanager
import threading
from pathlib import Path
import os

# Thread-local storage for correlation context
_correlation_context = threading.local()


@dataclass
class LogContext:
    """Container for logging context information."""
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    operation: Optional[str] = None
    stage: Optional[str] = None
    service_name: str = "enhanced-policy-generator"
    version: str = "1.0.0"
    environment: str = "development"
    additional_fields: Dict[str, Any] = field(default_factory=dict)


class CorrelationIdFilter(logging.Filter):
    """Logging filter that adds correlation ID to log records."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add correlation context to log record."""
        # Get correlation context from thread-local storage
        context = getattr(_correlation_context, 'context', None)
        
        if context:
            record.correlation_id = context.correlation_id
            record.user_id = context.user_id
            record.session_id = context.session_id
            record.request_id = context.request_id
            record.operation = context.operation
            record.stage = context.stage
            record.service_name = context.service_name
            record.version = context.version
            record.environment = context.environment
            
            # Add additional fields
            for key, value in context.additional_fields.items():
                setattr(record, key, value)
        else:
            # Set defaults if no context
            record.correlation_id = getattr(record, 'correlation_id', None)
            record.user_id = getattr(record, 'user_id', None)
            record.session_id = getattr(record, 'session_id', None)
            record.request_id = getattr(record, 'request_id', None)
            record.operation = getattr(record, 'operation', None)
            record.stage = getattr(record, 'stage', None)
            record.service_name = getattr(record, 'service_name', 'enhanced-policy-generator')
            record.version = getattr(record, 'version', '1.0.0')
            record.environment = getattr(record, 'environment', 'development')
        
        return True


class StructuredJSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def __init__(
        self,
        include_extra: bool = True,
        reserved_fields: Optional[List[str]] = None,
        timestamp_format: str = "iso",
        service_name: str = "enhanced-policy-generator"
    ):
        super().__init__()
        self.include_extra = include_extra
        self.service_name = service_name
        self.timestamp_format = timestamp_format
        
        # Fields that should not be included in 'extra' section
        self.reserved_fields = set(reserved_fields or [
            'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
            'module', 'exc_info', 'exc_text', 'stack_info', 'lineno', 'funcName',
            'created', 'msecs', 'relativeCreated', 'thread', 'threadName',
            'processName', 'process', 'getMessage', 'correlation_id', 'user_id',
            'session_id', 'request_id', 'operation', 'stage', 'service_name',
            'version', 'environment'
        ])
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        # Create base log entry
        log_entry = {
            "timestamp": self._format_timestamp(record.created),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "service": getattr(record, 'service_name', self.service_name),
            "version": getattr(record, 'version', '1.0.0'),
            "environment": getattr(record, 'environment', 'development'),
        }
        
        # Add correlation context
        correlation_fields = {
            "correlation_id": getattr(record, 'correlation_id', None),
            "user_id": getattr(record, 'user_id', None),
            "session_id": getattr(record, 'session_id', None),
            "request_id": getattr(record, 'request_id', None),
            "operation": getattr(record, 'operation', None),
            "stage": getattr(record, 'stage', None),
        }
        
        # Only include non-None correlation fields
        log_entry["context"] = {k: v for k, v in correlation_fields.items() if v is not None}
        
        # Add source information
        log_entry["source"] = {
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "filename": record.filename,
        }
        
        # Add thread and process information
        log_entry["execution"] = {
            "thread": record.thread,
            "thread_name": record.threadName,
            "process": record.process,
            "process_name": record.processName,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info) if record.exc_info else None,
            }
        
        # Add extra fields
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in self.reserved_fields and not key.startswith('_'):
                    # Handle complex objects by converting to string
                    if isinstance(value, (dict, list, tuple)):
                        try:
                            extra_fields[key] = value
                        except (TypeError, ValueError):
                            extra_fields[key] = str(value)
                    else:
                        extra_fields[key] = value
            
            if extra_fields:
                log_entry["extra"] = extra_fields
        
        return json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))
    
    def _format_timestamp(self, created: float) -> str:
        """Format timestamp according to configured format."""
        dt = datetime.fromtimestamp(created, tz=timezone.utc)
        
        if self.timestamp_format == "iso":
            return dt.isoformat()
        elif self.timestamp_format == "unix":
            return str(created)
        elif self.timestamp_format == "readable":
            return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3] + " UTC"
        else:
            return dt.isoformat()


class PerformanceFormatter(StructuredJSONFormatter):
    """Specialized formatter for performance logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format performance log with timing metrics."""
        log_entry = json.loads(super().format(record))
        
        # Add performance-specific fields
        performance_fields = {
            "duration_ms": getattr(record, 'duration_ms', None),
            "operation_type": getattr(record, 'operation_type', None),
            "success": getattr(record, 'success', None),
            "error_count": getattr(record, 'error_count', None),
            "retry_count": getattr(record, 'retry_count', None),
            "circuit_breaker_state": getattr(record, 'circuit_breaker_state', None),
        }
        
        # Only include non-None performance fields
        log_entry["performance"] = {k: v for k, v in performance_fields.items() if v is not None}
        
        return json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))


class PolicyGeneratorLogAdapter(logging.LoggerAdapter):
    """Logger adapter for Enhanced Policy Generator with automatic context."""
    
    def __init__(self, logger: logging.Logger, extra: Optional[Dict[str, Any]] = None):
        super().__init__(logger, extra or {})
    
    def process(self, msg: str, kwargs: Dict[str, Any]) -> tuple:
        """Process log message with context information."""
        # Merge adapter extra with kwargs extra
        extra = kwargs.get('extra', {})
        if self.extra:
            extra.update(self.extra)
        
        # Add correlation context if available
        context = getattr(_correlation_context, 'context', None)
        if context:
            extra.update({
                'correlation_id': context.correlation_id,
                'operation': context.operation,
                'stage': context.stage,
                'service_name': context.service_name,
            })
        
        kwargs['extra'] = extra
        return msg, kwargs
    
    def log_performance(
        self,
        level: int,
        operation: str,
        duration_ms: float,
        success: bool = True,
        error_count: int = 0,
        retry_count: int = 0,
        circuit_breaker_state: Optional[str] = None,
        **kwargs
    ):
        """Log performance metrics."""
        extra = kwargs.get('extra', {})
        extra.update({
            'operation_type': operation,
            'duration_ms': duration_ms,
            'success': success,
            'error_count': error_count,
            'retry_count': retry_count,
            'circuit_breaker_state': circuit_breaker_state,
        })
        kwargs['extra'] = extra
        
        msg = f"Performance: {operation} completed in {duration_ms:.2f}ms"
        if not success:
            msg += f" with {error_count} errors"
        if retry_count > 0:
            msg += f" after {retry_count} retries"
        
        self.log(level, msg, **kwargs)
    
    def log_stage_start(self, stage: str, correlation_id: Optional[str] = None, **kwargs):
        """Log the start of a policy generation stage."""
        extra = kwargs.get('extra', {})
        extra.update({
            'stage': stage,
            'stage_event': 'start',
            'correlation_id': correlation_id,
        })
        kwargs['extra'] = extra
        
        self.info(f"Starting stage: {stage}", **kwargs)
    
    def log_stage_complete(
        self,
        stage: str,
        duration_ms: float,
        success: bool = True,
        correlation_id: Optional[str] = None,
        **kwargs
    ):
        """Log the completion of a policy generation stage."""
        extra = kwargs.get('extra', {})
        extra.update({
            'stage': stage,
            'stage_event': 'complete',
            'duration_ms': duration_ms,
            'success': success,
            'correlation_id': correlation_id,
        })
        kwargs['extra'] = extra
        
        status = "completed" if success else "failed"
        self.info(f"Stage {stage} {status} in {duration_ms:.2f}ms", **kwargs)
    
    def log_ai_service_call(
        self,
        operation: str,
        model: str,
        prompt_tokens: Optional[int] = None,
        completion_tokens: Optional[int] = None,
        duration_ms: Optional[float] = None,
        success: bool = True,
        error: Optional[str] = None,
        correlation_id: Optional[str] = None,
        **kwargs
    ):
        """Log AI service calls with detailed metrics."""
        extra = kwargs.get('extra', {})
        extra.update({
            'ai_operation': operation,
            'ai_model': model,
            'prompt_tokens': prompt_tokens,
            'completion_tokens': completion_tokens,
            'total_tokens': (prompt_tokens or 0) + (completion_tokens or 0) if prompt_tokens and completion_tokens else None,
            'duration_ms': duration_ms,
            'success': success,
            'error': error,
            'correlation_id': correlation_id,
        })
        kwargs['extra'] = extra
        
        msg = f"AI Service: {operation} using {model}"
        if duration_ms:
            msg += f" in {duration_ms:.2f}ms"
        if not success and error:
            msg += f" - Error: {error}"
        
        level = logging.INFO if success else logging.ERROR
        self.log(level, msg, **kwargs)


def set_correlation_context(context: LogContext):
    """Set correlation context for current thread."""
    _correlation_context.context = context


def get_correlation_context() -> Optional[LogContext]:
    """Get current correlation context."""
    return getattr(_correlation_context, 'context', None)


def clear_correlation_context():
    """Clear correlation context for current thread."""
    if hasattr(_correlation_context, 'context'):
        delattr(_correlation_context, 'context')


@contextmanager
def correlation_context(
    correlation_id: Optional[str] = None,
    operation: Optional[str] = None,
    stage: Optional[str] = None,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    request_id: Optional[str] = None,
    **kwargs
):
    """Context manager for correlation tracking."""
    # Generate correlation ID if not provided
    if correlation_id is None:
        correlation_id = str(uuid.uuid4())
    
    # Create context
    context = LogContext(
        correlation_id=correlation_id,
        operation=operation,
        stage=stage,
        user_id=user_id,
        session_id=session_id,
        request_id=request_id,
        additional_fields=kwargs
    )
    
    # Store previous context
    previous_context = get_correlation_context()
    
    try:
        set_correlation_context(context)
        yield correlation_id
    finally:
        # Restore previous context
        if previous_context:
            set_correlation_context(previous_context)
        else:
            clear_correlation_context()


def generate_correlation_id() -> str:
    """Generate a new correlation ID."""
    return str(uuid.uuid4())


def configure_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    log_file: Optional[str] = None,
    enable_console: bool = True,
    service_name: str = "enhanced-policy-generator",
    environment: str = "development",
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
) -> Dict[str, Any]:
    """
    Configure enhanced logging for the Enhanced Policy Generator.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Format type ('json', 'text', 'performance')
        log_file: Path to log file (optional)
        enable_console: Whether to enable console logging
        service_name: Name of the service for log entries
        environment: Environment name (development, staging, production)
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
    
    Returns:
        Logging configuration dictionary
    """
    
    # Determine log level
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Configure formatters
    formatters = {}
    
    if log_format == "json":
        formatters["json"] = {
            "()": StructuredJSONFormatter,
            "include_extra": True,
            "service_name": service_name,
            "timestamp_format": "iso",
        }
        formatter_name = "json"
    elif log_format == "performance":
        formatters["performance"] = {
            "()": PerformanceFormatter,
            "include_extra": True,
            "service_name": service_name,
            "timestamp_format": "iso",
        }
        formatter_name = "performance"
    else:  # text format
        formatters["text"] = {
            "format": "%(asctime)s - %(name)s - %(levelname)s - [%(correlation_id)s] - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        }
        formatter_name = "text"
    
    # Configure filters
    filters = {
        "correlation_filter": {
            "()": CorrelationIdFilter,
        }
    }
    
    # Configure handlers
    handlers = {}
    root_handlers = []
    
    # Console handler
    if enable_console:
        handlers["console"] = {
            "class": "logging.StreamHandler",
            "level": log_level.upper(),
            "formatter": formatter_name,
            "filters": ["correlation_filter"],
            "stream": "ext://sys.stdout",
        }
        root_handlers.append("console")
    
    # File handler
    if log_file:
        # Ensure log directory exists
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        handlers["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": log_level.upper(),
            "formatter": formatter_name,
            "filters": ["correlation_filter"],
            "filename": str(log_path),
            "maxBytes": max_file_size,
            "backupCount": backup_count,
            "encoding": "utf-8",
        }
        root_handlers.append("file")
    
    # Configure loggers
    loggers = {
        "backend.services.enhanced_policy_generator": {
            "level": log_level.upper(),
            "handlers": root_handlers,
            "propagate": False,
        },
        "backend.utils": {
            "level": log_level.upper(),
            "handlers": root_handlers,
            "propagate": False,
        },
        "backend.exceptions": {
            "level": log_level.upper(),
            "handlers": root_handlers,
            "propagate": False,
        },
    }
    
    # Complete logging configuration
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": formatters,
        "filters": filters,
        "handlers": handlers,
        "loggers": loggers,
        "root": {
            "level": log_level.upper(),
            "handlers": root_handlers,
        }
    }
    
    return config


def setup_enhanced_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    log_file: Optional[str] = None,
    enable_console: bool = True,
    service_name: str = "enhanced-policy-generator",
    environment: Optional[str] = None,
) -> PolicyGeneratorLogAdapter:
    """
    Setup enhanced logging and return a configured logger adapter.
    
    Args:
        log_level: Logging level
        log_format: Format type ('json', 'text', 'performance')
        log_file: Optional log file path
        enable_console: Whether to enable console logging
        service_name: Service name for logs
        environment: Environment name
    
    Returns:
        Configured PolicyGeneratorLogAdapter instance
    """
    
    # Determine environment
    if environment is None:
        environment = os.getenv("ENVIRONMENT", "development")
    
    # Generate logging configuration
    config = configure_logging(
        log_level=log_level,
        log_format=log_format,
        log_file=log_file,
        enable_console=enable_console,
        service_name=service_name,
        environment=environment,
    )
    
    # Apply configuration
    logging.config.dictConfig(config)
    
    # Create and return logger adapter
    logger = logging.getLogger(service_name)
    adapter = PolicyGeneratorLogAdapter(logger, {
        "service_name": service_name,
        "environment": environment,
    })
    
    return adapter


def get_logger(name: str) -> PolicyGeneratorLogAdapter:
    """
    Get a logger adapter for the specified name.
    
    Args:
        name: Logger name
    
    Returns:
        PolicyGeneratorLogAdapter instance
    """
    logger = logging.getLogger(name)
    return PolicyGeneratorLogAdapter(logger)


# Predefined logger adapters for common use cases
def get_service_logger() -> PolicyGeneratorLogAdapter:
    """Get logger for the Enhanced Policy Generator service."""
    return get_logger("backend.services.enhanced_policy_generator")


def get_utils_logger() -> PolicyGeneratorLogAdapter:
    """Get logger for utility modules."""
    return get_logger("backend.utils")


def get_exceptions_logger() -> PolicyGeneratorLogAdapter:
    """Get logger for exception handling."""
    return get_logger("backend.exceptions")


# Performance monitoring utilities
@contextmanager
def performance_timer(
    logger: PolicyGeneratorLogAdapter,
    operation: str,
    log_level: int = logging.INFO,
    correlation_id: Optional[str] = None,
):
    """Context manager for timing operations and logging performance."""
    start_time = time.perf_counter()
    success = True
    error_count = 0
    
    try:
        yield
    except Exception:
        success = False
        error_count = 1
        raise
    finally:
        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        
        logger.log_performance(
            level=log_level,
            operation=operation,
            duration_ms=duration_ms,
            success=success,
            error_count=error_count,
            correlation_id=correlation_id,
        )


# Environment-specific configurations
DEVELOPMENT_CONFIG = {
    "log_level": "DEBUG",
    "log_format": "json",
    "enable_console": True,
    "log_file": "logs/enhanced-policy-generator-dev.log",
}

PRODUCTION_CONFIG = {
    "log_level": "INFO",
    "log_format": "json",
    "enable_console": False,
    "log_file": "/var/log/enhanced-policy-generator/app.log",
}

TESTING_CONFIG = {
    "log_level": "WARNING",
    "log_format": "text",
    "enable_console": True,
    "log_file": None,
}


def get_config_for_environment(env: str) -> Dict[str, Any]:
    """Get logging configuration for specific environment."""
    configs = {
        "development": DEVELOPMENT_CONFIG,
        "production": PRODUCTION_CONFIG,
        "testing": TESTING_CONFIG,
    }
    return configs.get(env, DEVELOPMENT_CONFIG)
