"""
Retry utilities with exponential backoff and jitter for Enhanced Policy Generator.

This module provides decorators and utilities for implementing robust retry
mechanisms with configurable backoff strategies, particularly for AI service calls.
"""

import asyncio
import random
import time
from functools import wraps
from typing import (
    Any, Callable, Optional, Union, Type, Tuple, List,
    Awaitable, TypeVar, Generic
)
from dataclasses import dataclass
import logging

from backend.exceptions.enhanced_policy_exceptions import (
    EnhancedPolicyException,
    AIServiceException,
    AIRateLimitException,
    TimeoutException
)

logger = logging.getLogger(__name__)

T = TypeVar('T')


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    jitter_range: float = 0.1
    timeout_seconds: Optional[float] = None
    
    # Exception types that should trigger retries
    retryable_exceptions: Tuple[Type[Exception], ...] = (
        AIServiceException,
        AIRateLimitException,
        ConnectionError,
        TimeoutError,
    )
    
    # Exception types that should NOT be retried
    non_retryable_exceptions: Tuple[Type[Exception], ...] = (
        ValueError,
        TypeError,
        KeyError,
    )


class ExponentialBackoffRetry:
    """
    Implements exponential backoff retry logic with jitter.
    
    Provides both sync and async retry mechanisms with configurable
    backoff strategies and exception handling.
    """
    
    def __init__(self, config: Optional[RetryConfig] = None):
        self.config = config or RetryConfig()
    
    def calculate_delay(self, attempt: int) -> float:
        """
        Calculate the delay for a given attempt number.
        
        Args:
            attempt: The attempt number (0-based)
            
        Returns:
            Delay in seconds
        """
        delay = self.config.base_delay * (self.config.exponential_base ** attempt)
        delay = min(delay, self.config.max_delay)
        
        if self.config.jitter:
            jitter_amount = delay * self.config.jitter_range
            jitter = random.uniform(-jitter_amount, jitter_amount)
            delay += jitter
        
        return max(0, delay)
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """
        Determine if an exception should trigger a retry.
        
        Args:
            exception: The exception that occurred
            attempt: Current attempt number (0-based)
            
        Returns:
            True if retry should be attempted
        """
        # Don't retry if we've exceeded max attempts
        if attempt >= self.config.max_attempts:
            return False
        
        # Check for non-retryable exceptions first
        if isinstance(exception, self.config.non_retryable_exceptions):
            return False
        
        # Check for retryable exceptions
        if isinstance(exception, self.config.retryable_exceptions):
            return True
        
        # For AIRateLimitException, always retry within limits
        if isinstance(exception, AIRateLimitException):
            return True
        
        # Default to not retrying unknown exceptions
        return False
    
    def get_retry_after_delay(self, exception: Exception) -> Optional[float]:
        """
        Extract retry-after information from exceptions.
        
        Args:
            exception: The exception that occurred
            
        Returns:
            Delay in seconds if specified by the exception
        """
        if hasattr(exception, 'retry_after') and exception.retry_after:
            return float(exception.retry_after)
        return None
    
    async def execute_async(
        self,
        func: Callable[..., Awaitable[T]],
        *args,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> T:
        """
        Execute an async function with retry logic.
        
        Args:
            func: The async function to execute
            *args: Positional arguments for the function
            correlation_id: Optional correlation ID for logging
            **kwargs: Keyword arguments for the function
            
        Returns:
            The result of the function call
            
        Raises:
            The last exception if all retries are exhausted
        """
        last_exception = None
        start_time = time.time()
        
        for attempt in range(self.config.max_attempts):
            try:
                # Check timeout
                if self.config.timeout_seconds:
                    elapsed = time.time() - start_time
                    if elapsed > self.config.timeout_seconds:
                        raise TimeoutException(
                            message=f"Operation timed out after {elapsed:.2f} seconds",
                            operation=func.__name__,
                            timeout_seconds=self.config.timeout_seconds,
                            elapsed_seconds=elapsed,
                            correlation_id=correlation_id
                        )
                
                # Execute the function
                logger.debug(
                    f"Attempting {func.__name__} (attempt {attempt + 1}/{self.config.max_attempts})",
                    extra={"correlation_id": correlation_id, "attempt": attempt}
                )
                
                result = await func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(
                        f"Function {func.__name__} succeeded on attempt {attempt + 1}",
                        extra={"correlation_id": correlation_id, "attempt": attempt}
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                logger.warning(
                    f"Function {func.__name__} failed on attempt {attempt + 1}: {str(e)}",
                    extra={"correlation_id": correlation_id, "attempt": attempt}
                )
                
                if not self.should_retry(e, attempt):
                    logger.error(
                        f"Not retrying {func.__name__} after attempt {attempt + 1}",
                        extra={"correlation_id": correlation_id, "exception": str(e)}
                    )
                    raise e
                
                if attempt < self.config.max_attempts - 1:
                    # Calculate delay
                    retry_after = self.get_retry_after_delay(e)
                    if retry_after:
                        delay = retry_after
                        logger.info(
                            f"Using retry-after delay of {delay:.2f} seconds",
                            extra={"correlation_id": correlation_id}
                        )
                    else:
                        delay = self.calculate_delay(attempt)
                    
                    logger.info(
                        f"Retrying {func.__name__} in {delay:.2f} seconds",
                        extra={"correlation_id": correlation_id, "delay": delay}
                    )
                    
                    await asyncio.sleep(delay)
        
        # All retries exhausted
        logger.error(
            f"All retries exhausted for {func.__name__}",
            extra={"correlation_id": correlation_id, "attempts": self.config.max_attempts}
        )
        raise last_exception
    
    def execute_sync(
        self,
        func: Callable[..., T],
        *args,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> T:
        """
        Execute a sync function with retry logic.
        
        Args:
            func: The function to execute
            *args: Positional arguments for the function
            correlation_id: Optional correlation ID for logging
            **kwargs: Keyword arguments for the function
            
        Returns:
            The result of the function call
            
        Raises:
            The last exception if all retries are exhausted
        """
        last_exception = None
        start_time = time.time()
        
        for attempt in range(self.config.max_attempts):
            try:
                # Check timeout
                if self.config.timeout_seconds:
                    elapsed = time.time() - start_time
                    if elapsed > self.config.timeout_seconds:
                        raise TimeoutException(
                            message=f"Operation timed out after {elapsed:.2f} seconds",
                            operation=func.__name__,
                            timeout_seconds=self.config.timeout_seconds,
                            elapsed_seconds=elapsed,
                            correlation_id=correlation_id
                        )
                
                # Execute the function
                logger.debug(
                    f"Attempting {func.__name__} (attempt {attempt + 1}/{self.config.max_attempts})",
                    extra={"correlation_id": correlation_id, "attempt": attempt}
                )
                
                result = func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(
                        f"Function {func.__name__} succeeded on attempt {attempt + 1}",
                        extra={"correlation_id": correlation_id, "attempt": attempt}
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                logger.warning(
                    f"Function {func.__name__} failed on attempt {attempt + 1}: {str(e)}",
                    extra={"correlation_id": correlation_id, "attempt": attempt}
                )
                
                if not self.should_retry(e, attempt):
                    logger.error(
                        f"Not retrying {func.__name__} after attempt {attempt + 1}",
                        extra={"correlation_id": correlation_id, "exception": str(e)}
                    )
                    raise e
                
                if attempt < self.config.max_attempts - 1:
                    # Calculate delay
                    retry_after = self.get_retry_after_delay(e)
                    if retry_after:
                        delay = retry_after
                        logger.info(
                            f"Using retry-after delay of {delay:.2f} seconds",
                            extra={"correlation_id": correlation_id}
                        )
                    else:
                        delay = self.calculate_delay(attempt)
                    
                    logger.info(
                        f"Retrying {func.__name__} in {delay:.2f} seconds",
                        extra={"correlation_id": correlation_id, "delay": delay}
                    )
                    
                    time.sleep(delay)
        
        # All retries exhausted
        logger.error(
            f"All retries exhausted for {func.__name__}",
            extra={"correlation_id": correlation_id, "attempts": self.config.max_attempts}
        )
        raise last_exception


def with_retry(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    jitter: bool = True,
    timeout_seconds: Optional[float] = None,
    retryable_exceptions: Optional[Tuple[Type[Exception], ...]] = None,
    non_retryable_exceptions: Optional[Tuple[Type[Exception], ...]] = None
):
    """
    Decorator for adding retry logic to async functions.
    
    Args:
        max_attempts: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        exponential_base: Base for exponential backoff
        jitter: Whether to add jitter to delays
        timeout_seconds: Optional overall timeout
        retryable_exceptions: Tuple of exception types that should trigger retries
        non_retryable_exceptions: Tuple of exception types that should not be retried
    
    Returns:
        Decorated function with retry logic
    """
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            config = RetryConfig(
                max_attempts=max_attempts,
                base_delay=base_delay,
                max_delay=max_delay,
                exponential_base=exponential_base,
                jitter=jitter,
                timeout_seconds=timeout_seconds,
                retryable_exceptions=retryable_exceptions or RetryConfig().retryable_exceptions,
                non_retryable_exceptions=non_retryable_exceptions or RetryConfig().non_retryable_exceptions,
            )
            
            retry_handler = ExponentialBackoffRetry(config)
            
            # Extract correlation_id if present in kwargs
            correlation_id = kwargs.pop('correlation_id', None)
            
            return await retry_handler.execute_async(
                func, *args, correlation_id=correlation_id, **kwargs
            )
        
        return wrapper
    return decorator


def with_retry_sync(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    jitter: bool = True,
    timeout_seconds: Optional[float] = None,
    retryable_exceptions: Optional[Tuple[Type[Exception], ...]] = None,
    non_retryable_exceptions: Optional[Tuple[Type[Exception], ...]] = None
):
    """
    Decorator for adding retry logic to sync functions.
    
    Args:
        max_attempts: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        exponential_base: Base for exponential backoff
        jitter: Whether to add jitter to delays
        timeout_seconds: Optional overall timeout
        retryable_exceptions: Tuple of exception types that should trigger retries
        non_retryable_exceptions: Tuple of exception types that should not be retried
    
    Returns:
        Decorated function with retry logic
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> T:
            config = RetryConfig(
                max_attempts=max_attempts,
                base_delay=base_delay,
                max_delay=max_delay,
                exponential_base=exponential_base,
                jitter=jitter,
                timeout_seconds=timeout_seconds,
                retryable_exceptions=retryable_exceptions or RetryConfig().retryable_exceptions,
                non_retryable_exceptions=non_retryable_exceptions or RetryConfig().non_retryable_exceptions,
            )
            
            retry_handler = ExponentialBackoffRetry(config)
            
            # Extract correlation_id if present in kwargs
            correlation_id = kwargs.pop('correlation_id', None)
            
            return retry_handler.execute_sync(
                func, *args, correlation_id=correlation_id, **kwargs
            )
        
        return wrapper
    return decorator


# Predefined retry configurations for different scenarios
AI_SERVICE_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    base_delay=1.0,
    max_delay=120.0,
    exponential_base=2.0,
    jitter=True,
    timeout_seconds=300.0,  # 5 minutes total timeout
    retryable_exceptions=(
        AIServiceException,
        AIRateLimitException,
        ConnectionError,
        TimeoutError,
        OSError,  # Network issues
    ),
    non_retryable_exceptions=(
        ValueError,
        TypeError,
        KeyError,
        AttributeError,
    )
)

QUICK_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=0.5,
    max_delay=10.0,
    exponential_base=1.5,
    jitter=True,
    timeout_seconds=30.0,
)

AGGRESSIVE_RETRY_CONFIG = RetryConfig(
    max_attempts=10,
    base_delay=0.1,
    max_delay=30.0,
    exponential_base=1.2,
    jitter=True,
    timeout_seconds=600.0,  # 10 minutes
)


def create_ai_service_retry() -> ExponentialBackoffRetry:
    """Create a retry handler optimized for AI service calls."""
    return ExponentialBackoffRetry(AI_SERVICE_RETRY_CONFIG)


def create_quick_retry() -> ExponentialBackoffRetry:
    """Create a retry handler for quick operations."""
    return ExponentialBackoffRetry(QUICK_RETRY_CONFIG)


def create_aggressive_retry() -> ExponentialBackoffRetry:
    """Create a retry handler for critical operations that need many attempts."""
    return ExponentialBackoffRetry(AGGRESSIVE_RETRY_CONFIG)


def with_retry_config(config: RetryConfig):
    """
    Decorator for adding retry logic to async functions using a RetryConfig object.

    Args:
        config: RetryConfig object with all retry parameters

    Returns:
        Decorated function with retry logic
    """
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            retry_handler = ExponentialBackoffRetry(config)

            # Extract correlation_id if present in kwargs
            correlation_id = kwargs.pop('correlation_id', None)

            return await retry_handler.execute_async(
                func, *args, correlation_id=correlation_id, **kwargs
            )

        return wrapper
    return decorator