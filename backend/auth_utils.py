# backend/auth_utils.py
from fastapi import HTTPException, Header, Depends
from jose import jwt, JWTError # JWTError is a base class for jose exceptions like ExpiredSignatureError
from typing import Dict
import motor.motor_asyncio

# Import configurations from backend.config
from backend.config import JWT_SECRET, JWT_ALGORITHM
from .database import get_db # Import get_db for dependency injection

def verify_jwt_token(token: str) -> Dict:
    """
    Verifies the JWT token and returns the payload.
    Raises HTTPException if the token is invalid or expired.
    This function is synchronous as jose.jwt.decode is synchronous.
    """
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        # Ensure user_id is in payload, as it's critical
        if "user_id" not in payload:
            raise HTTPException(status_code=401, detail="Invalid token: user_id missing from payload")
        return payload
    except JWTError as e: # Catches ExpiredSignatureError, InvalidTokenError, etc.
        error_detail = "Invalid token"
        if "expired" in str(e).lower() or isinstance(e, jwt.ExpiredSignatureError):
            error_detail = "Token has expired"
        raise HTTPException(status_code=401, detail=error_detail)

async def get_user_from_token(
    authorization: str = Header(None),
    db: motor.motor_asyncio.AsyncIOMotorDatabase = Depends(get_db)
) -> Dict:
    """
    Extracts user information from the authorization header token.
    Verifies the token and uses the provided database connection to fetch user details.
    Removes sensitive fields ('password', '_id') from the returned user dictionary.
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    parts = authorization.split(" ")
    if len(parts) != 2 or parts[0].strip().lower() != "bearer":
        raise HTTPException(status_code=401, detail="Invalid authorization header format. Must be 'Bearer <token>'.")
    
    token = parts[1]
    
    try:
        payload = verify_jwt_token(token) # Synchronous call
    except HTTPException as e: # Propagate HTTPException from verify_jwt_token
        raise e

    user_id = payload["user_id"] # Already checked in verify_jwt_token

    user = await db.users.find_one({"id": user_id}) # Async database call
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Remove sensitive data before returning
    user_response = user.copy()
    if "password" in user_response:
        del user_response["password"]
    if "_id" in user_response: # MongoDB's default primary key
        del user_response["_id"]
            
    return user_response


async def get_current_user_id(authorization: str = Header(None)) -> str:
    """
    FastAPI dependency to get the current user's ID from the JWT token.
    Extracts token from Authorization header, verifies it, and returns user_id.
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")

    parts = authorization.split(" ")
    if len(parts) != 2 or parts[0].strip().lower() != "bearer":
        raise HTTPException(status_code=401, detail="Invalid authorization header format. Must be 'Bearer <token>'.")

    token = parts[1]

    try:
        payload = verify_jwt_token(token)  # verify_jwt_token is synchronous
        # user_id is guaranteed to be in payload by verify_jwt_token if it doesn't raise an exception
        return payload["user_id"]
    except HTTPException as e:  # Propagate HTTPException from verify_jwt_token
        raise e
