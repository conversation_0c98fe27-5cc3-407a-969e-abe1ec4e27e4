#!/usr/bin/env python3
"""
Test script to verify Stage 1 & 2 implementation (Data Collection and Risk Assessment)
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from backend.services.enhanced_policy_generator import PolicyPromptManager
from backend.models import PolicyRequest

async def test_stage_implementation():
    """Test the Stage 1 & 2 implementation"""
    
    print("🚀 Testing Enhanced Policy Generator - Stage 1 & 2 Implementation")
    print("=" * 70)
    
    # Create a test request
    request = PolicyRequest(
        framework='gdpr',
        company_name='TestCorp',
        company_type='Tech Startup',
        industry='SaaS',
        employee_count=50,
        data_types=['PII', 'Financial Records'],
        existing_policies=['Password Policy'],
        complexity_level='High',
        user_id='test_user_123',
        jurisdiction='EU',
        risk_profile='High'
    )
    
    print(f"📋 Test Request:")
    print(f"   Company: {request.company_name}")
    print(f"   Framework: {request.framework}")
    print(f"   Industry: {request.industry}")
    print(f"   Risk Profile: {request.risk_profile}")
    print()
    
    try:
        # Initialize the prompt manager
        pm = PolicyPromptManager()
        print("✅ PolicyPromptManager initialized successfully")
        
        # Test Stage 1: Data Collection Prompts
        print("\n📊 STAGE 1: DATA COLLECTION")
        print("-" * 40)
        
        # Test organizational data collection
        org_prompt = pm.get_data_collection_prompt(request, 'organizational')
        print(f"✅ Organizational prompt generated: {len(org_prompt):,} characters")
        print(f"   Contains company name: {'✅' if request.company_name in org_prompt else '❌'}")
        print(f"   Contains framework: {'✅' if request.framework in org_prompt else '❌'}")
        
        # Test regulatory data collection
        reg_prompt = pm.get_data_collection_prompt(request, 'regulatory')
        print(f"✅ Regulatory prompt generated: {len(reg_prompt):,} characters")
        print(f"   Contains jurisdiction: {'✅' if request.jurisdiction in reg_prompt else '❌'}")
        
        # Test industry data collection
        industry_prompt = pm.get_data_collection_prompt(request, 'industry')
        print(f"✅ Industry prompt generated: {len(industry_prompt):,} characters")
        print(f"   Contains industry: {'✅' if request.industry in industry_prompt else '❌'}")
        
        # Test Stage 2: Risk Assessment Prompts
        print("\n⚠️  STAGE 2: RISK ASSESSMENT")
        print("-" * 40)
        
        sample_data = """
        ORGANIZATIONAL DATA:
        TestCorp is a SaaS company with 50 employees processing PII and Financial Records.
        
        REGULATORY ENVIRONMENT:
        Subject to GDPR requirements in the EU jurisdiction.
        
        INDUSTRY CONTEXT:
        Operating in the SaaS industry with high risk profile.
        """
        
        # Test technical risk assessment
        tech_risk_prompt = pm.get_risk_assessment_prompt(request, sample_data, 'technical')
        print(f"✅ Technical risk prompt generated: {len(tech_risk_prompt):,} characters")
        print(f"   Contains collected data: {'✅' if 'TestCorp' in tech_risk_prompt else '❌'}")
        
        # Test operational risk assessment
        ops_risk_prompt = pm.get_risk_assessment_prompt(request, sample_data, 'operational')
        print(f"✅ Operational risk prompt generated: {len(ops_risk_prompt):,} characters")
        print(f"   Contains risk scoring: {'✅' if 'Risk Scoring' in ops_risk_prompt else '❌'}")
        
        # Test compliance risk assessment
        comp_risk_prompt = pm.get_risk_assessment_prompt(request, sample_data, 'compliance')
        print(f"✅ Compliance risk prompt generated: {len(comp_risk_prompt):,} characters")
        print(f"   Contains framework: {'✅' if request.framework in comp_risk_prompt else '❌'}")
        
        # Summary
        print("\n🎉 IMPLEMENTATION SUMMARY")
        print("=" * 40)
        print("✅ Stage 1 (Data Collection) - IMPLEMENTED")
        print("   ✅ Organizational data collection prompts")
        print("   ✅ Regulatory environment analysis prompts")
        print("   ✅ Industry context analysis prompts")
        print()
        print("✅ Stage 2 (Risk Assessment) - IMPLEMENTED")
        print("   ✅ Technical risk assessment prompts")
        print("   ✅ Operational risk assessment prompts")
        print("   ✅ Compliance risk assessment prompts")
        print()
        print("🚀 Enhanced Policy Generator Stage 1 & 2 implementation is COMPLETE!")
        print("   Ready for integration with AI service for full pipeline testing.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_stage_implementation())
    sys.exit(0 if success else 1)
