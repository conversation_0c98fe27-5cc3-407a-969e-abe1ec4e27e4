import requests
import sys
import json
from datetime import datetime

class ComplianceGPTTester:
    def __init__(self, base_url="https://5fb85ea3-d551-4232-b367-a6d8d4edbcbe.preview.emergentagent.com/api"):
        self.base_url = base_url
        self.tests_run = 0
        self.tests_passed = 0
        self.test_results = []

    def run_test(self, name, method, endpoint, expected_status, data=None, headers=None):
        """Run a single API test"""
        url = f"{self.base_url}/{endpoint}"
        default_headers = {'Content-Type': 'application/json'}
        if headers:
            default_headers.update(headers)
        
        self.tests_run += 1
        print(f"\n🔍 Testing {name}...")
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=default_headers)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=default_headers)
            elif method == 'PUT':
                response = requests.put(url, json=data, headers=default_headers)
            
            success = response.status_code == expected_status
            
            result = {
                "name": name,
                "method": method,
                "endpoint": endpoint,
                "expected_status": expected_status,
                "actual_status": response.status_code,
                "success": success
            }
            
            if success:
                self.tests_passed += 1
                print(f"✅ Passed - Status: {response.status_code}")
                try:
                    result["response"] = response.json()
                except:
                    result["response"] = response.text
            else:
                print(f"❌ Failed - Expected {expected_status}, got {response.status_code}")
                try:
                    result["error"] = response.json()
                except:
                    result["error"] = response.text
            
            self.test_results.append(result)
            return success, response
            
        except Exception as e:
            print(f"❌ Failed - Error: {str(e)}")
            self.test_results.append({
                "name": name,
                "method": method,
                "endpoint": endpoint,
                "expected_status": expected_status,
                "success": False,
                "error": str(e)
            })
            return False, None

    def test_health_check(self):
        """Test the health check endpoint"""
        return self.run_test(
            "Health Check",
            "GET",
            "health",
            200
        )

    def test_get_frameworks(self):
        """Test getting available compliance frameworks"""
        return self.run_test(
            "Get Frameworks",
            "GET",
            "frameworks",
            200
        )

    def test_get_specific_framework(self):
        """Test getting a specific framework"""
        return self.run_test(
            "Get Specific Framework (GDPR)",
            "GET",
            "frameworks/gdpr",
            200
        )

    def test_get_dashboard(self):
        """Test getting dashboard data"""
        return self.run_test(
            "Get Dashboard Data",
            "GET",
            "dashboard",
            200
        )

    def test_generate_policy(self):
        """Test AI policy generation"""
        data = {
            "framework": "gdpr",
            "company_name": "TestCorp Ltd",
            "company_type": "SaaS",
            "employee_count": 50,
            "industry": "Software Development",
            "data_types": ["Personal Data"],
            "existing_policies": [],
            "complexity_level": "standard"
        }
        return self.run_test(
            "Generate AI Policy",
            "POST",
            "policies/generate",
            200,
            data=data
        )

    def test_get_policies(self):
        """Test getting all policies"""
        return self.run_test(
            "Get All Policies",
            "GET",
            "policies",
            200
        )

    def test_create_project(self):
        """Test creating a compliance project"""
        return self.run_test(
            "Create Compliance Project",
            "POST",
            "projects?framework=gdpr&company_name=TestCorp&target_date=2025-06-01",
            200
        )

    def test_get_projects(self):
        """Test getting all projects"""
        return self.run_test(
            "Get All Projects",
            "GET",
            "projects",
            200
        )

    def test_get_available_integrations(self):
        """Test getting available integrations"""
        return self.run_test(
            "Get Available Integrations",
            "GET",
            "evidence/integrations",
            200
        )

    def test_get_configured_integrations(self):
        """Test getting configured integrations"""
        return self.run_test(
            "Get Configured Integrations",
            "GET",
            "evidence/integrations/configured",
            200
        )

    def test_setup_integration(self):
        """Test setting up an integration"""
        data = {
            "provider": "google_workspace",
            "name": "Test Google Workspace",
            "credentials": {"demo_mode": True, "access_token": "demo_token_123"},
            "settings": {"collect_frequency": "daily"}
        }
        return self.run_test(
            "Setup Integration",
            "POST",
            "evidence/integrations/setup",
            200,
            data=data
        )

    def test_get_evidence(self):
        """Test getting evidence"""
        return self.run_test(
            "Get Evidence",
            "GET",
            "evidence/evidence",
            200
        )

    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*50)
        print(f"📊 Test Summary: {self.tests_passed}/{self.tests_run} tests passed")
        print("="*50)
        
        if self.tests_passed < self.tests_run:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['name']}: Expected {result['expected_status']}, got {result.get('actual_status', 'Error')}")
                    if "error" in result:
                        print(f"    Error: {result['error']}")
        
        return self.tests_passed == self.tests_run

def main():
    # Get base URL from command line if provided
    base_url = "https://5fb85ea3-d551-4232-b367-a6d8d4edbcbe.preview.emergentagent.com/api"
    
    print(f"🚀 Testing ComplianceGPT API at {base_url}")
    tester = ComplianceGPTTester(base_url)
    
    # Run tests
    tester.test_health_check()
    tester.test_get_frameworks()
    tester.test_get_specific_framework()
    tester.test_get_dashboard()
    tester.test_get_policies()
    tester.test_get_projects()
    tester.test_get_available_integrations()
    tester.test_get_configured_integrations()
    
    # Run tests that modify data
    tester.test_setup_integration()
    tester.test_get_evidence()
    tester.test_create_project()
    
    # Run policy generation test last as it's the most intensive
    tester.test_generate_policy()
    
    # Print summary
    success = tester.print_summary()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())